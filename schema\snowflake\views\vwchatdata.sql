﻿CREATE OR REPLACE VIEW vwchatdata AS
SELECT
    c.keyid,
    c.conversationid,
    c.conversationstart,
    c.conversationstartltc,
    c.userid,
    u.name AS username,
    u.department,
    u.title,
    c.chatinitiatedby,
    c.agentchatcount,
    c.agentchattotal,
    c.agentchatmax,
    c.agentchatmin,
    c.agent<PERSON>read,
    c.custchatcount,
    c.custchattotal,
    c.custchatmax,
    c.custchatmin,
    c.updated,
    c.mediatype,
    CASE
        WHEN c.mediatype = 'chat' THEN 'Chat'
        WHEN c.mediatype = 'message' THEN 'Message'
        ELSE 'Unknown'
    END AS mediatypedisplay
FROM chatdata c
LEFT JOIN userdetails u ON c.userid = u.id;
