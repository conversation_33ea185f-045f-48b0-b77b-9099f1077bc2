CREATE TABLE IF NOT EXISTS userinteractiondata (
    keyid varchar(255) NOT NULL,
    userid varchar(50),
    direction varchar(50),
    queueid varchar(50),
    mediatype varchar(50),
    wrapupcode varchar(255),
    startdate timestamp without time zone NOT NULL,
    startdateltc timestamp without time zone NOT NULL,
    talertcount integer,
    talerttimesum numeric(20, 2),
    talerttimemax numeric(20, 2),
    talerttimemin numeric(20, 2),
    tansweredcount integer,
    tansweredtimesum numeric(20, 2),
    tansweredtimemax numeric(20, 2),
    tansweredtimemin numeric(20, 2),
    ttalkcount integer,
    ttalktimesum numeric(20, 2),
    ttalktimemax numeric(20, 2),
    ttalktimemin numeric(20, 2),
    ttalkcompletecount integer,
    ttalkcompletetimesum numeric(20, 2),
    ttalkcompletetimemax numeric(20, 2),
    ttalkcompletetimemin numeric(20, 2),
    tnotrespondingcount integer,
    tnotrespondingtimesum numeric(20, 2),
    tnotrespondingtimemax numeric(20, 2),
    tnotrespondingtimemin numeric(20, 2),
    theldcount integer,
    theldtimesum numeric(20, 2),
    theldtimemax numeric(20, 2),
    theldtimemin numeric(20, 2),
    theldcompletecount integer,
    theldcompletetimesum numeric(20, 2),
    theldcompletetimemax numeric(20, 2),
    theldcompletetimemin numeric(20, 2),
    thandlecount integer,
    thandletimesum numeric(20, 2),
    thandletimemax numeric(20, 2),
    thandletimemin numeric(20, 2),
    tacwcount integer,
    tacwtimesum numeric(20, 2),
    tacwtimemax numeric(20, 2),
    tacwtimemin numeric(20, 2),
    nconsult integer,
    nconsulttransferred integer,
    noutbound integer,
    nerror integer,
    ntransferred integer,
    nblindtransferred integer,
    nconnected integer,
    tdialingcount integer,
    tdialingtimesum numeric(20, 2),
    tdialingtimemax numeric(20, 2),
    tdialingtimemin numeric(20, 2),
    tcontactingcount integer,
    tcontactingtimesum numeric(20, 2),
    tcontactingtimemax numeric(20, 2),
    tcontactingtimemin numeric(20, 2),
    tvoicemailcount integer,
    tvoicemailtimesum numeric(20, 2),
    tvoicemailtimemax numeric(20, 2),
    tvoicemailtimemin numeric(20, 2),
    tuserresponsetimecount integer,
    tuserresponsetimetimesum numeric(20, 2),
    tuserresponsetimetimemax numeric(20, 2),
    tuserresponsetimetimemin numeric(20, 2),
    tagentresponsetimecount integer,
    tagentresponsetimetimesum numeric(20, 2),
    tagentresponsetimetimemax numeric(20, 2),
    tagentresponsetimetimemin numeric(20, 2),
    av1count integer,
    av1timesum numeric(20, 2),
    av1timemax numeric(20, 2),
    av1timemin numeric(20, 2),
    av2count integer,
    av2timesum numeric(20, 2),
    av2timemax numeric(20, 2),
    av2timemin numeric(20, 2),
    av3count integer,
    av3timesum numeric(20, 2),
    av3timemax numeric(20, 2),
    av3timemin numeric(20, 2),
    av4count integer,
    av4timesum numeric(20, 2),
    av4timemax numeric(20, 2),
    av4timemin numeric(20, 2),
    av5count integer,
    av5timesum numeric(20, 2),
    av5timemax numeric(20, 2),
    av5timemin numeric(20, 2),
    av6count integer,
    av6timesum numeric(20, 2),
    av6timemax numeric(20, 2),
    av6timemin numeric(20, 2),
    av7count integer,
    av7timesum numeric(20, 2),
    av7timemax numeric(20, 2),
    av7timemin numeric(20, 2),
    av8count integer,
    av8timesum numeric(20, 2),
    av8timemax numeric(20, 2),
    av8timemin numeric(20, 2),
    av9count integer,
    av9timesum numeric(20, 2),
    av9timemax numeric(20, 2),
    av9timemin numeric(20, 2),
    av10count integer,
    av10timesum numeric(20, 2),
    av10timemax numeric(20, 2),
    av10timemin numeric(20, 2),
    noutboundabandoned integer,
    noutboundattempted integer,
    noutboundconnected integer,
    nstatetransitionerror integer,
    oexternalmediacount integer,
    tmonitoringcount integer,
    tmonitoringtimesum numeric(20, 2),
    tmonitoringtimemax numeric(20, 2),
    tmonitoringtimemin numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT userinteractiondata_pkey PRIMARY KEY (keyid, startdate)
) PARTITION BY RANGE (startdate);

CREATE INDEX IF NOT EXISTS "UserInteractionDataDate" ON userinteractiondata USING btree (
    userid ASC NULLS LAST,
    startdate ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS "UserInteractionDataDateLTC" ON userinteractiondata USING btree (
    userid varchar_ops ASC NULLS LAST,
    startdateltc ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS "UserInteractionDataMedia" ON userinteractiondata USING btree (
    mediatype ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS "UserInteractionDataQueue" ON userinteractiondata USING btree (
    queueid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS "UserInteractionDataUser" ON userinteractiondata USING btree (
    userid ASC NULLS LAST
);

CREATE INDEX IF NOT EXISTS userinteractiondatawrapup ON userinteractiondata USING btree (
    wrapupcode ASC NULLS LAST
);

ALTER TABLE IF EXISTS userintdate_2020_01 RENAME TO userinteractiondata_p2020_01;
ALTER TABLE IF EXISTS userintdate_2020_02 RENAME TO userinteractiondata_p2020_02;
ALTER TABLE IF EXISTS userintdate_2020_03 RENAME TO userinteractiondata_p2020_03;
ALTER TABLE IF EXISTS userintdate_2020_04 RENAME TO userinteractiondata_p2020_04;
ALTER TABLE IF EXISTS userintdate_2020_05 RENAME TO userinteractiondata_p2020_05;
ALTER TABLE IF EXISTS userintdate_2020_06 RENAME TO userinteractiondata_p2020_06;
ALTER TABLE IF EXISTS userintdate_2020_07 RENAME TO userinteractiondata_p2020_07;
ALTER TABLE IF EXISTS userintdate_2020_08 RENAME TO userinteractiondata_p2020_08;
ALTER TABLE IF EXISTS userintdate_2020_09 RENAME TO userinteractiondata_p2020_09;
ALTER TABLE IF EXISTS userintdate_2020_10 RENAME TO userinteractiondata_p2020_10;
ALTER TABLE IF EXISTS userintdate_2020_11 RENAME TO userinteractiondata_p2020_11;
ALTER TABLE IF EXISTS userintdate_2020_12 RENAME TO userinteractiondata_p2020_12;
ALTER TABLE IF EXISTS userintdate_2021_01 RENAME TO userinteractiondata_p2021_01;
ALTER TABLE IF EXISTS userintdate_2021_02 RENAME TO userinteractiondata_p2021_02;
ALTER TABLE IF EXISTS userintdate_2021_03 RENAME TO userinteractiondata_p2021_03;
ALTER TABLE IF EXISTS userintdate_2021_04 RENAME TO userinteractiondata_p2021_04;
ALTER TABLE IF EXISTS userintdate_2021_05 RENAME TO userinteractiondata_p2021_05;
ALTER TABLE IF EXISTS userintdate_2021_06 RENAME TO userinteractiondata_p2021_06;
ALTER TABLE IF EXISTS userintdate_2021_07 RENAME TO userinteractiondata_p2021_07;
ALTER TABLE IF EXISTS userintdate_2021_08 RENAME TO userinteractiondata_p2021_08;
ALTER TABLE IF EXISTS userintdate_2021_09 RENAME TO userinteractiondata_p2021_09;
ALTER TABLE IF EXISTS userintdate_2021_10 RENAME TO userinteractiondata_p2021_10;
ALTER TABLE IF EXISTS userintdate_2021_11 RENAME TO userinteractiondata_p2021_11;
ALTER TABLE IF EXISTS userintdate_2021_12 RENAME TO userinteractiondata_p2021_12;
ALTER TABLE IF EXISTS userintdate_2022_01 RENAME TO userinteractiondata_p2022_01;
ALTER TABLE IF EXISTS userintdate_2022_02 RENAME TO userinteractiondata_p2022_02;
ALTER TABLE IF EXISTS userintdate_2022_03 RENAME TO userinteractiondata_p2022_03;
ALTER TABLE IF EXISTS userintdate_2022_04 RENAME TO userinteractiondata_p2022_04;
ALTER TABLE IF EXISTS userintdate_2022_05 RENAME TO userinteractiondata_p2022_05;
ALTER TABLE IF EXISTS userintdate_2022_06 RENAME TO userinteractiondata_p2022_06;
ALTER TABLE IF EXISTS userintdate_2022_07 RENAME TO userinteractiondata_p2022_07;
ALTER TABLE IF EXISTS userintdate_2022_08 RENAME TO userinteractiondata_p2022_08;
ALTER TABLE IF EXISTS userintdate_2022_09 RENAME TO userinteractiondata_p2022_09;
ALTER TABLE IF EXISTS userintdate_2022_10 RENAME TO userinteractiondata_p2022_10;
ALTER TABLE IF EXISTS userintdate_2022_11 RENAME TO userinteractiondata_p2022_11;
ALTER TABLE IF EXISTS userintdate_2022_12 RENAME TO userinteractiondata_p2022_12;
ALTER TABLE IF EXISTS userintdate_2023_01 RENAME TO userinteractiondata_p2023_01;

-- Add missing user-specific metrics columns
ALTER TABLE userinteractiondata ADD COLUMN IF NOT EXISTS noutboundabandoned integer;
ALTER TABLE userinteractiondata ADD COLUMN IF NOT EXISTS noutboundattempted integer;
ALTER TABLE userinteractiondata ADD COLUMN IF NOT EXISTS noutboundconnected integer;
ALTER TABLE userinteractiondata ADD COLUMN IF NOT EXISTS nstatetransitionerror integer;
ALTER TABLE userinteractiondata ADD COLUMN IF NOT EXISTS oexternalmediacount integer;
ALTER TABLE userinteractiondata ADD COLUMN IF NOT EXISTS tmonitoringcount integer;
ALTER TABLE userinteractiondata ADD COLUMN IF NOT EXISTS tmonitoringtimesum numeric(20, 2);
ALTER TABLE userinteractiondata ADD COLUMN IF NOT EXISTS tmonitoringtimemax numeric(20, 2);
ALTER TABLE userinteractiondata ADD COLUMN IF NOT EXISTS tmonitoringtimemin numeric(20, 2);

-- Add comments

COMMENT ON COLUMN userInteractionData.av10count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av10timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av10timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av10timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av1count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av1timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av1timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av1timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av2count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av2timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av2timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av2timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av3count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av3timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av3timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av3timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av4count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av4timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av4timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av4timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av5count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av5timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av5timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av5timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av6count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av6timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av6timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av6timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av7count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av7timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av7timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av7timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av8count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av8timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av8timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av8timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av9count IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av9timemax IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av9timemin IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.av9timesum IS 'Custom Configurable Field - Please Talk to UCA About the use of Aggregation Queries in the adapter'; 
COMMENT ON COLUMN userInteractionData.direction IS 'Conversation Direction'; 
COMMENT ON COLUMN userInteractionData.keyid IS 'Primary Key'; 
COMMENT ON COLUMN userInteractionData.mediatype IS 'Conversation Media Type'; 
COMMENT ON COLUMN userInteractionData.nblindtransferred IS 'Total Blind Transfer Count'; 
COMMENT ON COLUMN userInteractionData.nconnected IS 'Total Conversations Connected Count'; 
COMMENT ON COLUMN userInteractionData.nconsult IS 'Total Consult Count'; 
COMMENT ON COLUMN userInteractionData.nconsulttransferred IS 'Total Consult Transfer Count'; 
COMMENT ON COLUMN userInteractionData.nerror IS 'Total Error Count'; 
COMMENT ON COLUMN userInteractionData.noutbound IS 'Total OutBound Conversations'; 
COMMENT ON COLUMN userInteractionData.ntransferred IS 'Total Transfer Count'; 
COMMENT ON COLUMN userInteractionData.queueid IS 'Queue GUID'; 
COMMENT ON COLUMN userInteractionData.startdate IS 'Interval Start Date (UTC)'; 
COMMENT ON COLUMN userInteractionData.startdateltc IS 'Interval Start Date (UTC)'; 
COMMENT ON COLUMN userInteractionData.tacwcount IS 'Total ACW Count'; 
COMMENT ON COLUMN userInteractionData.tacwtimemax IS 'Max ACW Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tacwtimemin IS 'Min ACW Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tacwtimesum IS 'Total ACW Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tagentresponsetimecount IS 'Total Agent Response Count'; 
COMMENT ON COLUMN userInteractionData.tagentresponsetimetimemax IS 'Max Agent Response Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tagentresponsetimetimemin IS 'Min Agent Response Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tagentresponsetimetimesum IS 'Total Agent Response Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.talertcount IS 'Total Alert Count'; 
COMMENT ON COLUMN userInteractionData.talerttimemax IS 'Max Alert Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.talerttimemin IS 'Min ACW Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.talerttimesum IS 'Total Alert Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tansweredcount IS 'Total Answered Count'; 
COMMENT ON COLUMN userInteractionData.tansweredtimemax IS 'Max Answered Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tansweredtimemin IS 'Min Answered Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tansweredtimesum IS 'Total Answered Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tcontactingcount IS 'Total Contacting Count'; 
COMMENT ON COLUMN userInteractionData.tcontactingtimemax IS 'Max Contacting Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tcontactingtimemin IS 'Min Contacting Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tcontactingtimesum IS 'Total Contacting Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tdialingcount IS 'Total Dialing Count'; 
COMMENT ON COLUMN userInteractionData.tdialingtimemax IS 'Max Dialing Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tdialingtimemin IS 'Min Dialing Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tdialingtimesum IS 'Total Dialing Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.thandlecount IS 'Total Handle Count'; 
COMMENT ON COLUMN userInteractionData.thandletimemax IS 'Max Handle Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.thandletimemin IS 'Min Handle Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.thandletimesum IS 'Total Handle Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.theldcompletecount IS 'Total Hold Count for Completed Conversations'; 
COMMENT ON COLUMN userInteractionData.theldcompletetimemax IS 'Max Hold Time for Completed Conversations'; 
COMMENT ON COLUMN userInteractionData.theldcompletetimemin IS 'in Hold Time for Completed Conversations'; 
COMMENT ON COLUMN userInteractionData.theldcompletetimesum IS 'Total Hold Time for Completed Conversations'; 
COMMENT ON COLUMN userInteractionData.theldcount IS 'Total Hold Count'; 
COMMENT ON COLUMN userInteractionData.theldtimemax IS 'Max Hold Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.theldtimemin IS 'Min Hold Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.theldtimesum IS 'Total Hold Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tnotrespondingcount IS 'Total Not Responding Count'; 
COMMENT ON COLUMN userInteractionData.tnotrespondingtimemax IS 'Max Not Responding Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tnotrespondingtimemin IS 'Min Not Responding Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tnotrespondingtimesum IS 'Total Not Responding Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.ttalkcompletecount IS 'Total Talk Count For Completed Conversations'; 
COMMENT ON COLUMN userInteractionData.ttalkcompletetimemax IS 'Max Talk Time For Completed Conversations'; 
COMMENT ON COLUMN userInteractionData.ttalkcompletetimemin IS 'Min Talk Time For Completed Conversations'; 
COMMENT ON COLUMN userInteractionData.ttalkcompletetimesum IS 'Total Talk Time For Completed Conversations'; 
COMMENT ON COLUMN userInteractionData.ttalkcount IS 'Total Talk Count'; 
COMMENT ON COLUMN userInteractionData.ttalktimemax IS 'Max Talk Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.ttalktimemin IS 'Min Talk Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.ttalktimesum IS 'Total Talk Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tuserresponsetimecount IS 'Total User Response Count'; 
COMMENT ON COLUMN userInteractionData.tuserresponsetimetimemin IS 'Min User Response Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tuserresponsetimetimemax IS 'Max User Response Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tuserresponsetimetimesum IS 'Total User Response Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tvoicemailcount IS 'Total Voicemail Count'; 
COMMENT ON COLUMN userInteractionData.tvoicemailtimemax IS 'Max Voicemail Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tvoicemailtimemin IS 'Min Voicemail Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.tvoicemailtimesum IS 'Total Voicemail Time (Seconds)'; 
COMMENT ON COLUMN userInteractionData.updated IS 'Date Row Updated (UTC)'; 
COMMENT ON COLUMN userInteractionData.userid IS 'Agent GUID'; 
COMMENT ON COLUMN userInteractionData.wrapupcode IS 'Conversation Wrap Up GUID';
COMMENT ON COLUMN userInteractionData.noutboundabandoned IS 'Total Outbound Abandoned Count';
COMMENT ON COLUMN userInteractionData.noutboundattempted IS 'Total Outbound Attempted Count';
COMMENT ON COLUMN userInteractionData.noutboundconnected IS 'Total Outbound Connected Count';
COMMENT ON COLUMN userInteractionData.nstatetransitionerror IS 'Total State Transition Error Count';
COMMENT ON COLUMN userInteractionData.oexternalmediacount IS 'External Media Count';
COMMENT ON COLUMN userInteractionData.tmonitoringcount IS 'Total Monitoring Count';
COMMENT ON COLUMN userInteractionData.tmonitoringtimemax IS 'Max Monitoring Time (Seconds)';
COMMENT ON COLUMN userInteractionData.tmonitoringtimemin IS 'Min Monitoring Time (Seconds)';
COMMENT ON COLUMN userInteractionData.tmonitoringtimesum IS 'Total Monitoring Time (Seconds)';
COMMENT ON TABLE userInteractionData IS 'User Interaction Data Interval Data - Interval is from (15-60) Min(s)';