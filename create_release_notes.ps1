﻿#!/usr/bin/env pwsh
<#
.SYNOPSIS
    Create HTML release notes for Genesys Adapter based on git tag analysis
.DESCRIPTION
    This script provides a streamlined approach to generating release notes by:
    1. Identifying the last two git tags
    2. Analyzing actual code changes between tags
    3. Generating HTML formatted for monthly categorization
    4. Supporting both new monthly sections and additions to existing months
.EXAMPLE
    .\create_release_notes.ps1
    .\create_release_notes.ps1 -FromTag "v3.48.5" -ToTag "v3.48.6.0"
#>

param(
    [string]$FromTag = "",
    [string]$ToTag = "",
    [switch]$ExistingMonth = $false
)

function Get-GitTags {
    $tags = git tag --sort=-version:refname | Select-Object -First 10
    if ($tags.Count -lt 2) {
        throw "Not enough tags found. Need at least 2 tags to generate release notes."
    }
    return @{
        Latest = $tags[0]
        Previous = $tags[1]
    }
}

function Get-TagDate {
    param([string]$Tag)

    $tagInfo = git log --tags --simplify-by-decoration --pretty="format:%ai %d" | Where-Object { $_ -match $Tag }
    if ($tagInfo) {
        $dateStr = ($tagInfo -split '\s+')[0..2] -join ' '
        return [DateTime]::Parse($dateStr)
    }
    return Get-Date
}

function Get-ChangedFiles {
    param([string]$FromTag, [string]$ToTag)

    return git diff --name-only "$FromTag..$ToTag"
}

function Analyze-Changes {
    param([string]$FromTag, [string]$ToTag, [array]$Files)

    $changes = @{
        DataConsistency = @()
        TechnicalEnhancements = @()
        DatabaseUpdates = @()
        AffectedComponents = @()
    }

    foreach ($file in $Files) {
        $diff = git diff "$FromTag..$ToTag" -- $file

        # Analyze specific patterns in the code changes
        if ($diff -match "OrderBy|Sort.*participants|Sort.*sessions|Sort.*segments") {
            $changes.DataConsistency += "Fixed duplicate conversation segments by implementing consistent sorting of participants, sessions, and segments"
        }

        if ($diff -match "isolation|ProcessContactListsWithIsolationAsync") {
            $changes.DataConsistency += "Enhanced contact list processing with isolated processing to prevent data cross-contamination"
        }

        if ($diff -match "FirstQueueId|LastQueueId.*null.*check") {
            $changes.DataConsistency += "Improved queue ID assignment logic with proper null handling"
        }

        if ($diff -match "null.*safety|null.*check") {
            $changes.TechnicalEnhancements += "Added robust null safety checks throughout conversation data processing"
        }

        if ($diff -match "async.*Task|ProcessContactListsWithIsolationAsync") {
            $changes.TechnicalEnhancements += "Enhanced asynchronous processing for contact list operations"
        }

        if ($diff -match "keyid.*format.*validation") {
            $changes.TechnicalEnhancements += "Implemented comprehensive keyid format validation and correction"
        }

        if ($file -match "\.sql$" -and $diff -match "datekeyfield") {
            $changes.DatabaseUpdates += "Simplified database sync scripts for conversation summary data across all database types"
        }

        # Track affected components
        if ($file -match "OutboundDialingData") {
            $changes.AffectedComponents += "Outbound Dialing Data processing"
        }
        if ($file -match "SegmentData") {
            $changes.AffectedComponents += "Conversation Segment Data processing"
        }
        if ($file -match "schema.*functions") {
            $changes.AffectedComponents += "Database synchronization functions"
        }
    }

    # Remove duplicates
    $changes.DataConsistency = $changes.DataConsistency | Select-Object -Unique
    $changes.TechnicalEnhancements = $changes.TechnicalEnhancements | Select-Object -Unique
    $changes.DatabaseUpdates = $changes.DatabaseUpdates | Select-Object -Unique
    $changes.AffectedComponents = $changes.AffectedComponents | Select-Object -Unique

    return $changes
}

function Generate-ReleaseHTML {
    param(
        [string]$Version,
        [DateTime]$ReleaseDate,
        $Changes,
        [bool]$IsNewMonth
    )

    $monthYear = $ReleaseDate.ToString("MMMM yyyy")
    $dateStr = $ReleaseDate.ToString("MMMM d, yyyy")

    $versionHtml = @"
<div class="release-version">
    <h3>Version $Version - $dateStr</h3>

"@

    if ($Changes.DataConsistency.Count -gt 0) {
        $versionHtml += @"
    <h4>Data Consistency Improvements</h4>
    <ul>
"@
        foreach ($item in $Changes.DataConsistency) {
            $versionHtml += "        <li>$item</li>`n"
        }
        $versionHtml += "    </ul>`n    `n"
    }

    if ($Changes.TechnicalEnhancements.Count -gt 0) {
        $versionHtml += @"
    <h4>Technical Enhancements</h4>
    <ul>
"@
        foreach ($item in $Changes.TechnicalEnhancements) {
            $versionHtml += "        <li>$item</li>`n"
        }
        $versionHtml += "    </ul>`n    `n"
    }

    if ($Changes.DatabaseUpdates.Count -gt 0) {
        $versionHtml += @"
    <h4>Database Updates</h4>
    <ul>
"@
        foreach ($item in $Changes.DatabaseUpdates) {
            $versionHtml += "        <li>$item</li>`n"
        }
        $versionHtml += "    </ul>`n    `n"
    }

    if ($Changes.AffectedComponents.Count -gt 0) {
        $versionHtml += @"
    <h4>Affected Components</h4>
    <ul>
"@
        foreach ($item in $Changes.AffectedComponents) {
            $versionHtml += "        <li>$item</li>`n"
        }
        $versionHtml += "    </ul>`n"
    }

    $versionHtml += "</div>"

    if ($IsNewMonth) {
        return @"
<div class="release-month">
    <h2>$monthYear</h2>
    $versionHtml
</div>
"@
    } else {
        return $versionHtml
    }
}

# Main execution
try {
    Write-Host "Genesys Adapter Release Notes Generator" -ForegroundColor Cyan
    Write-Host "=======================================" -ForegroundColor Cyan

    # Get tags to compare
    if (-not $FromTag -or -not $ToTag) {
        $tagInfo = Get-GitTags
        $ToTag = $tagInfo.Latest
        $FromTag = $tagInfo.Previous

        Write-Host "`nLast two tags found:" -ForegroundColor Yellow
        Write-Host "  Latest: $ToTag" -ForegroundColor Green
        Write-Host "  Previous: $FromTag" -ForegroundColor Green

        $confirmation = Read-Host "`nAre these the correct tags to compare? (y/n)"
        if ($confirmation -ne 'y' -and $confirmation -ne 'Y') {
            Write-Host "Exiting. Please run again with -FromTag and -ToTag parameters" -ForegroundColor Red
            exit 1
        }
    }

    # Get release date and determine if new month
    $releaseDate = Get-TagDate -Tag $ToTag
    $monthYear = $releaseDate.ToString("MMMM yyyy")

    if (-not $ExistingMonth) {
        $monthChoice = Read-Host "`nIs this a new monthly section for $monthYear? (y/n)"
        $isNewMonth = ($monthChoice -eq 'y' -or $monthChoice -eq 'Y')
    } else {
        $isNewMonth = $false
    }

    Write-Host "`nAnalyzing changes between $FromTag and $ToTag..." -ForegroundColor Green

    # Get changed files and analyze
    $changedFiles = Get-ChangedFiles -FromTag $FromTag -ToTag $ToTag
    Write-Host "Found $($changedFiles.Count) changed files" -ForegroundColor Yellow

    $changes = Analyze-Changes -FromTag $FromTag -ToTag $ToTag -Files $changedFiles

    # Generate HTML
    $html = Generate-ReleaseHTML -Version $ToTag -ReleaseDate $releaseDate -Changes $changes -IsNewMonth $isNewMonth

    # Output results
    Write-Host "`n" -NoNewline
    if ($isNewMonth) {
        Write-Host "Generated NEW MONTHLY SECTION for $monthYear" -ForegroundColor Green
    } else {
        Write-Host "Generated VERSION SECTION to ADD to existing $monthYear" -ForegroundColor Green
    }
    Write-Host "=" * 60 -ForegroundColor Green
    Write-Output $html

    # Save to file
    $outputFile = if ($isNewMonth) { "release_notes_${monthYear}_new.html".Replace(" ", "_") } else { "release_notes_${ToTag}_add.html".Replace(".", "_") }
    $html | Out-File -FilePath $outputFile -Encoding UTF8
    Write-Host "`nHTML saved to: $outputFile" -ForegroundColor Green

    Write-Host "`nSummary of changes:" -ForegroundColor Cyan
    Write-Host "- Data Consistency: $($changes.DataConsistency.Count) improvements" -ForegroundColor White
    Write-Host "- Technical Enhancements: $($changes.TechnicalEnhancements.Count) items" -ForegroundColor White
    Write-Host "- Database Updates: $($changes.DatabaseUpdates.Count) items" -ForegroundColor White
    Write-Host "- Affected Components: $($changes.AffectedComponents.Count) components" -ForegroundColor White

} catch {
    Write-Error "Error generating release notes: $_"
    exit 1
}
