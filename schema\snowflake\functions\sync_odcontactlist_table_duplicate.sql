DELETE FROM odcontactlistdata
WHERE EXISTS (
    SELECT 1
    FROM tabledefinitions
    WHERE tablename = 'odcontactlistdata'
      AND version LIKE '3.49.%'
      AND version < '3.49.2'
)
AND inin_outbound_id IN (
    SELECT inin_outbound_id
    FROM (
        SELECT inin_outbound_id,
               ROW_NUMBER() OVER (
                   PARTITION BY inin_outbound_id
                   ORDER BY updated ASC
               ) AS rn
        FROM odcontactlistdata
        WHERE inin_outbound_id IN (
            SELECT inin_outbound_id
            FROM odcontactlistdata
            GROUP BY inin_outbound_id
            HAVING COUNT(DISTINCT contactlistid) > 1
        )
    )
    WHERE rn = 1
);
