﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.ApplicationInsights;
using Microsoft.Extensions.Logging;
using StandardUtils;

namespace GCRealTime
{
    public class GCRealTime
    {
        private readonly ILogger _logger;
        private readonly TelemetryClient _telemetry;

        public GCRealTime(ILogger logger, TelemetryClient telemetry)
        {
            _logger = logger;
            _telemetry = telemetry;
        }

        public void RunRealTime()
        {
            Utils Utils = new Utils();
            _logger.LogInformation("RealTime Start " + DateTime.Now);
            // QueueObsRealTime QueueObsData = new QueueObsRealTime();

            UserRealTime UserAct = new UserRealTime(_logger);
            UserAct.SyncType = "userActivity";
            UserAct.Initialize();

            Thread ThUserAct = new Thread(new ThreadStart(UserAct.StartUserActivity));
            ThUserAct.Start();
            
            // System.Timers.Timer userActivityTimer = new System.Timers.Timer(TimeSpan.FromHours(1).TotalMilliseconds);
            // userActivityTimer.Elapsed += (sender, e) =>
            // {
            //     _logger.LogInformation("Restarting UserActivity thread.");
            //     UserAct.ShouldExit = true;
            //     ThUserAct.Join(TimeSpan.FromSeconds(30));
            //     UserAct = new UserRealTime(_logger);
            //     UserAct.SyncType = "userActivity";
            //     UserAct.Initialize();
            //     ThUserAct = new Thread(new ThreadStart(UserAct.StartUserActivity));
            //     ThUserAct.Start();
            // };
            // userActivityTimer.Start();

            Thread ThUserAdh = new Thread(new ThreadStart(UserAct.StartUserAdherence));
            ThUserAdh.Start();

            Thread ThUserCall = new Thread(new ThreadStart(UserAct.StartUserCalls));
            ThUserCall.Start();

            UserRealTime UserRealTime = new UserRealTime(_logger);
            UserRealTime.SyncType = "userCalls";
            UserRealTime.Initialize();

            Thread ThUserCallDets = new Thread(new ThreadStart(UserRealTime.StartUserCallDets));
            ThUserCallDets.Start();


            UserRealTime QVRealTime = new UserRealTime(_logger);
            QVRealTime.SyncType = "queueCalls";
            QVRealTime.Initialize();


            Thread ThQueueCallDets = new Thread(new ThreadStart(QVRealTime.StartQueueCallDets));
            ThQueueCallDets.Start();

            // UserRealTime QueueObs = new UserRealTime(_logger);
            // QueueObs.SyncType = "queueObservations";
            // QueueObs.Initialize();

            // Thread ThQueueObservation = new Thread(new ThreadStart(QueueObs.StartQueueObservations));
            // ThQueueObservation.Start();

            var runningTime = System.Diagnostics.Stopwatch.StartNew();
            var reportTimer = System.Diagnostics.Stopwatch.StartNew();
            while (true)
            {
                Thread.Sleep(10000);
                if (reportTimer.Elapsed > TimeSpan.FromMinutes(10))
                {
                    _logger?.LogInformation("Realtime reporting heartbeat");
                    var props = new Dictionary<string,string>{};
                    var metrics = new Dictionary<string,double>{{"RunningTime", runningTime.Elapsed.TotalSeconds}};
                    _telemetry?.TrackEvent("Heartbeat", props, metrics);
                    reportTimer.Restart();
                }

                _logger.LogInformation("Realtime running for {0}", runningTime.Elapsed);
                // Log special status for QueueCallDets since it's designed to complete and exit after prefill
                string queueCallDetsStatus = ThQueueCallDets.ThreadState == ThreadState.Stopped
                    ? "Completed (prefill task finished)"
                    : $"{ThQueueCallDets.ThreadState} (errors: {QVRealTime.TotalErrors})";

                _logger.LogInformation("Realtime thread status - UserAct: {UserActState} (errors: {UserActErrors}), UserAdh: {UserAdhState}, UserCall: {UserCallState}, UserCallDets: {UserCallDetsState} (errors: {UserCallDetsErrors}), QueueCallDets: {QueueCallDetsStatus}",
                    ThUserAct.ThreadState, UserAct.TotalErrors,
                    ThUserAdh.ThreadState,
                    ThUserCall.ThreadState,
                    ThUserCallDets.ThreadState, UserRealTime.TotalErrors,
                    queueCallDetsStatus);
                // Console.WriteLine("Thread Queue Obs Dets Status :{0}, errors {1}", ThQueueObservation.ThreadState, QVRealTime.TotalErrors);

                // TODO: Inter-container locking
                //if (IsLockTaken("GenesysCloudDataAdapter", nameof(CSG.Adapter.Configuration.Job.Install)))
                //{
                //    _logger.LogWarning("{0} An install is currently running, shutting down workers...", DateTime.Now.ToString("s"));
                //    UserAct.ShouldExit = true;
                //    UserRealTime.ShouldExit = true;
                //    QVRealTime.ShouldExit = true;
                //}
                if (UserAct.ShouldExit || UserRealTime.ShouldExit || QVRealTime.ShouldExit
                    || ThUserAct.ThreadState == ThreadState.Stopped || ThUserAct.ThreadState == ThreadState.Aborted
                    || ThUserAdh.ThreadState == ThreadState.Stopped || ThUserAdh.ThreadState == ThreadState.Aborted
                    || ThUserCall.ThreadState == ThreadState.Stopped || ThUserCall.ThreadState == ThreadState.Aborted
                    || ThUserCallDets.ThreadState == ThreadState.Stopped || ThUserCallDets.ThreadState == ThreadState.Aborted
                    || ThQueueCallDets.ThreadState == ThreadState.Aborted) // Note: Removed ThreadState.Stopped for QueueCallDets - it's a one-time prefill task
                {
                    if (UserAct.ShouldExit || UserRealTime.ShouldExit || QVRealTime.ShouldExit)
                        _logger.LogInformation("Exit requested");
                    else
                        _logger.LogWarning("Exiting due to thread stopped");

                    UserAct.ShouldExit = true;
                    UserRealTime.ShouldExit = true;
                    QVRealTime.ShouldExit = true;
                    ThUserAct.Join(TimeSpan.FromSeconds(30));
                    ThUserAdh.Join(TimeSpan.FromSeconds(30));
                    ThUserCall.Join(TimeSpan.FromSeconds(30));
                    ThUserCallDets.Join(TimeSpan.FromSeconds(30));
                    // ThQueueObservation.Join(TimeSpan.FromSeconds(30));
                    ThQueueCallDets.Join(TimeSpan.FromSeconds(30));
                    break;
                }
            }

        }
    }
}
