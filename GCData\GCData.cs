﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Net;
using GenesysCloudUtils;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.Console;
using StandardUtils;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json.Linq;

namespace GCData
{
    public class GCGetData
    {
        // Public properties
        public DateTime UserPresenceLastUpdate { get; set; }
        public DateTime UserInteractionLastUpdate { get; set; }
        public DateTime WFMScheduleLastUpdate { get; set; }
        public DateTime QueueInteractionLastUpdate { get; set; }
        public DateTime QueueUserAuditLastUpdate { get; set; }
        public DateTime AdherenceLastUpdate { get; set; }
        public DateTime AdherenceFromDate { get; set; }
        public DateTime DetailInteractionLastUpdate { get; set; }
        public DateTime DetailEvaluationLastUpdate { get; set; }
        public DateTime DetailChatLastUpdate { get; set; }
        public DateTime DetailMessageLastUpdate { get; set; }
        public DateTime ShrinkageLastUpdate { get; set; }
        public DateTime LastUpdate { get; set; }
        public DateTime DateToSyncFrom { get; set; }
        public TimeSpan MaxSpanToSync { get; set; }
        public TimeSpan LookBackSpan { get; set; }
        public string TimeZoneConfig { get; set; }
        public string AggInterval { get; set; }
        public string UserAggViews { get; set; }
        public string QueueAggViews { get; set; }

        private const int WFMDaysMaxProcess = 20;
        private readonly DBUtils.DBUtils _dbConnector = new DBUtils.DBUtils();
        private readonly ILogger? _logger;
        private bool _enableForcedUpdatePermissions = false;

        /// <summary>
        /// Parameterless constructor. The logger will be null.
        /// </summary>
        public GCGetData() : this(null) { }

        /// <summary>
        /// Constructs a GCGetData instance with the provided logger.
        /// </summary>
        /// <param name="logger">The logger to use for logging messages.</param>
        public GCGetData(ILogger? logger)
        {
            _logger = logger;
        }

        public void Initialize(string SyncType, bool enableForcedUpdatePermissions = false)
        {
            _enableForcedUpdatePermissions = enableForcedUpdatePermissions;

            _dbConnector.Initialize();
            DataTable jobMinimumDefinition = _dbConnector.GetSQLTableData("select * from jobminimumdefinition", "jobminimumdefinition");

            if (!string.IsNullOrEmpty(SyncType))
                DateToSyncFrom = _dbConnector.GetSyncLastUpdate(SyncType);

            Utils UCAUtils = new Utils();
            DataTable ClientFeatures = UCAUtils.GetGCCustomerConfig();

            string jobName = CSG.Adapter.Compatability.LegacyOptions.GetOption("Job");

            DataView dataView = new DataView(jobMinimumDefinition)
            {
                RowFilter = $"JobName = '{jobName}'"
            };
            DataTable filteredJobMinimumDefinition = dataView.ToTable();

            DataRow row = filteredJobMinimumDefinition.Rows[0];
            string minMaxSyncSpan = row["MaxSyncSpan"]?.ToString() ?? "1.00:00:00";
            string minLookBackSpan = row["LookBackSpan"]?.ToString() ?? "1.00:00:00";

            if (!TimeSpan.TryParse(minMaxSyncSpan, out TimeSpan parsedMinimumMaxSyncSpan))
            {
                parsedMinimumMaxSyncSpan = TimeSpan.FromDays(1);
            }

            if (!TimeSpan.TryParse(minLookBackSpan, out TimeSpan parsedMinimumLookBackSpan))
            {
                parsedMinimumLookBackSpan = TimeSpan.FromDays(1);
            }

            // MaxSyncSpan
            if (!TimeSpan.TryParse(CSG.Adapter.Compatability.LegacyOptions.GetOption("MaxSyncSpan"), out TimeSpan parsedMaxSyncSpan))
            {
                parsedMaxSyncSpan = parsedMinimumMaxSyncSpan;
            }
            // Set a reasonable minimum for MaxSyncSpan (6 hours)
            TimeSpan absoluteMinimumMaxSyncSpan = TimeSpan.FromHours(1);

            if (parsedMaxSyncSpan < absoluteMinimumMaxSyncSpan)
            {
                _logger?.LogWarning("Configured MaxSyncSpan {ConfiguredSpan} is less than absolute minimum {MinimumSpan}. Using absolute minimum instead.",
                    parsedMaxSyncSpan, absoluteMinimumMaxSyncSpan);
                MaxSpanToSync = absoluteMinimumMaxSyncSpan;
            }
            else if (parsedMaxSyncSpan < parsedMinimumMaxSyncSpan)
            {
                _logger?.LogWarning("Configured MaxSyncSpan {ConfiguredSpan} is less than recommended minimum {RecommendedMinimum}. Using configured value anyway.",
                    parsedMaxSyncSpan, parsedMinimumMaxSyncSpan);
                MaxSpanToSync = parsedMaxSyncSpan;
            }
            else
            {
                MaxSpanToSync = parsedMaxSyncSpan;
            }

            TimeSpan parsedLookBack = TimeSpan.FromDays(1);
            if (!TimeSpan.TryParse(
                CSG.Adapter.Compatability.LegacyOptions.GetOption("LookBackSpan"),
                out parsedLookBack))
            {
                parsedLookBack = parsedMinimumLookBackSpan;
            }
            LookBackSpan = parsedLookBack < parsedMinimumLookBackSpan ? parsedMinimumLookBackSpan : parsedLookBack;

            // Condensed logging: Show all sync configuration in a single log entry
            TimeSpan totalEffectiveWindow = LookBackSpan + MaxSpanToSync;
            DateTime effectiveStartTime = DateToSyncFrom.Subtract(LookBackSpan);
            DateTime effectiveEndTime = DateToSyncFrom.Add(MaxSpanToSync);

            _logger?.LogInformation("Job:{JobName} - Sync Window: {EffectiveStart} to {EffectiveEnd} | " +
                "MaxSyncSpan={MaxSyncSpan}, LookBackSpan={LookBackSpan}, TotalWindow={TotalWindow}",
                jobName, effectiveStartTime, effectiveEndTime, MaxSpanToSync, LookBackSpan, totalEffectiveWindow);

            TimeZoneConfig = Convert.ToString(ClientFeatures.Rows[0]["datetimezone"]);
            AggInterval = Convert.ToString(ClientFeatures.Rows[0]["Interval"]);
            UserAggViews = Convert.ToString(ClientFeatures.Rows[0]["useraggviews"]);
            QueueAggViews = Convert.ToString(ClientFeatures.Rows[0]["queueaggviews"]);
        }

        public DataTable ActiveQMembersData()
        {
            DataTable QueueDetails = _dbConnector.GetSQLTableData("select * from queuedetails", "queuedetails");
            BUData BusUnitData = new BUData();
            BusUnitData.TimeZoneConfig = TimeZoneConfig;
            BusUnitData.Initialize();
            DataTable ActiveQMembers = BusUnitData.ActiveQMembers(QueueDetails);
            return ActiveQMembers;
        }

        public DataTable HeadcountForecastData()
        {
            try
            {
                DataTable ScheduleDetails = _dbConnector.GetSQLTableData("select * from scheduleDetails", "scheduleDetails");

                // Check if ScheduleDetails is null or empty
                if (ScheduleDetails == null || ScheduleDetails.Rows.Count == 0)
                {
                    Console.WriteLine("Warning: No schedule details found in database");
                    return new DataTable(); // Return empty table rather than null
                }

                BUData BusUnitData = new BUData();
                BusUnitData.TimeZoneConfig = TimeZoneConfig;
                BusUnitData.Initialize();
                DataTable HeadcountForecast = BusUnitData.HeadCountForecast(ScheduleDetails);

                return HeadcountForecast ?? new DataTable(); // Return empty table if null
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in HeadcountForecastData: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
                return new DataTable(); // Return empty table rather than propagating exception
            }
        }

        public DataTable OfferedForecastData(string StartDate)
        {
            DataTable BusinessUnitDetails = _dbConnector.GetSQLTableData("select * from buDetails", "buDetails");
            BUData BusUnitData = new BUData();
            BusUnitData.TimeZoneConfig = TimeZoneConfig;
            BusUnitData.Initialize();
            DataTable HeadcountForecast = BusUnitData.OfferedForecast(BusinessUnitDetails, StartDate);
            return HeadcountForecast;
        }

        public DataTable WFMScheduleDetails()
        {
            BUData GCWFMScheduleDetails = new BUData();
            GCWFMScheduleDetails.TimeZoneConfig = TimeZoneConfig;
            GCWFMScheduleDetails.Initialize();
            string StartDate = DateToSyncFrom.ToString("yyyy-MM-ddTHH:00:00.000Z");
            DataTable ScheduleDetails = GCWFMScheduleDetails.GetScheduleDetailsFromCC(StartDate);
            return ScheduleDetails;
        }

        public DataTable SystemCallUsageData(int DayOffset)
        {
            adminData GCSystemCallUsageData = new adminData(_logger);
            GCSystemCallUsageData.TimeZoneConfig = TimeZoneConfig;
            GCSystemCallUsageData.Initialize();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable OauthUsageData = GCSystemCallUsageData.GetSystemCallUsage(DayOffset);

            _logger?.LogDebug("System call usage data: {Count} rows retrieved", OauthUsageData.Rows.Count);
            return OauthUsageData;
        }

        public DataTable HoursBlockData(int MonthOffset)
        {
            UserData GCHoursBlockData = new UserData();
            GCHoursBlockData.TimeZoneConfig = TimeZoneConfig;
            GCHoursBlockData.Initialize();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable HoursBlockData = GCHoursBlockData.GetHoursBlockData(MonthOffset);

            _logger?.LogDebug("Hours block data: {Count} rows retrieved", HoursBlockData.Rows.Count);
            return HoursBlockData;
        }

        public DataTable OauthUsageData(int MonthOffset)
        {
            adminData GCOauthUsageData = new adminData(_logger);
            GCOauthUsageData.TimeZoneConfig = TimeZoneConfig;
            GCOauthUsageData.Initialize();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable OauthUsageData = GCOauthUsageData.GetOauthUsage(MonthOffset);

            _logger?.LogDebug("OAuth usage data: {Count} rows retrieved", OauthUsageData.Rows.Count);
            return OauthUsageData;
        }

        public DataTable SubHoursData()
        {
            adminData GCSubscriptionData = new adminData(_logger);
            GCSubscriptionData.TimeZoneConfig = TimeZoneConfig;
            GCSubscriptionData.Initialize();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable SubHoursData = GCSubscriptionData.GetSubUserUsage(DateToSyncFrom);

            _logger?.LogDebug("Subscription hours data: {Count} rows retrieved", SubHoursData.Rows.Count);
            return SubHoursData;
        }

        public DataTable SubOverviewData()
        {
            adminData GCSubscriptionData = new adminData(_logger);
            GCSubscriptionData.TimeZoneConfig = TimeZoneConfig;
            GCSubscriptionData.Initialize();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            DataTable SubscriptionData = GCSubscriptionData.GetSubscriptionOverViewDatafromGC();
            return SubscriptionData;
        }

        public DataTable UserPresenceData(DataTable Users)
        {
            UserData GCUserPresenceData = new UserData();
            GCUserPresenceData.TimeZoneConfig = TimeZoneConfig;
            GCUserPresenceData.AggInterval = AggInterval;
            GCUserPresenceData.Initialize();
            DataTable Presences = _dbConnector.GetSQLTableData("select * from presenceDetails", "presenceDetails");
            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            GCUserPresenceData.UserPresenceLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
            // Fix: Use full precision for end date to avoid data loss
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
            }
            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            DataTable UserPresenceData = GCUserPresenceData.GetUserPresenceDataFromGC(Users, Presences, StartDate, EndDate);
            return UserPresenceData;
        }

        public DataTable TimeOffReqData()
        {
            BUData GCTimeOffRequestData = new BUData();
            GCTimeOffRequestData.TimeZoneConfig = TimeZoneConfig;
            GCTimeOffRequestData.Initialize();
            DataTable TimeOffRequestData = GCTimeOffRequestData.GetTimeOffDataFromGC();
            return TimeOffRequestData;
        }

        public DataTable UserPresenceDetailedData()
        {
            UserData GCUserPresenceDetailedData = new UserData();
            GCUserPresenceDetailedData.TimeZoneConfig = TimeZoneConfig;
            GCUserPresenceDetailedData.Initialize();
            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00");
            // Note: This format doesn't have 'Z' suffix, so we need to specify it's UTC
            GCUserPresenceDetailedData.UserPresenceLastUpdate = DateTime.SpecifyKind(DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss", null), DateTimeKind.Utc);
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DateTime originalEndDate = DTEndDate;
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
                _logger?.LogDebug("Adjusted end date from {OriginalDate} to {AdjustedDate} (current time constraint)",
                    originalEndDate, DTEndDate);
            }
            String EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:00");
            DataTable UserPresenceData = GCUserPresenceDetailedData.GetUserDetailedPresenceFromGC(StartDate, EndDate);
            if (UserPresenceData != null)
            {
                if (GCUserPresenceDetailedData.UserPresenceLastUpdate > UserPresenceLastUpdate)
                    UserPresenceLastUpdate = GCUserPresenceDetailedData.UserPresenceLastUpdate;
            }
            UserPresenceLastUpdate = DTEndDate;
            return UserPresenceData;
        }

        public DataSet AdherenceData()
        {
            BUData GCAdherenceData = new BUData();
            TimeZoneInfo AppTimeZone = TimeZoneInfo.FindSystemTimeZoneById(TimeZoneConfig);
            GCAdherenceData.TimeZoneConfig = TimeZoneConfig;
            GCAdherenceData.Initialize();
            DateTime FromDate = DateToSyncFrom;
            Console.WriteLine("Initial sync date: {0}", FromDate);
            double TotalDays = MaxSpanToSync.TotalDays;
            TotalDays = TotalDays > 31 ? 31 : TotalDays;
            double TotalLookBackDays = LookBackSpan.TotalDays;
            DateTime StartDateDT = FromDate.AddDays(-TotalLookBackDays).Date;
            DateTime EndDateDT = FromDate.AddDays(TotalDays).Date;
            DateTime UTCFromDate = DateTime.SpecifyKind(StartDateDT, DateTimeKind.Utc);
            double Minutes = AppTimeZone.GetUtcOffset(UTCFromDate).TotalMinutes;
            int RemoveHrs = (int)(Minutes / 60);

            Console.WriteLine("Lookback Span: {0}", TotalLookBackDays);
            Console.WriteLine("Initial Querying Start Date   :{0}", StartDateDT);

            StartDateDT = StartDateDT.AddHours(-RemoveHrs);
            EndDateDT = EndDateDT.AddHours(-RemoveHrs);
            Console.WriteLine("Timezone adjustment: {0} hours", RemoveHrs);
            Console.WriteLine("Adjusted query start date: {0}", StartDateDT);
            bool isDstOld = AppTimeZone.IsDaylightSavingTime(StartDateDT);
            DateTime EndDateDTOld = EndDateDT;
            bool dstChange = false;
            for (var dt = StartDateDT; dt <= EndDateDT; dt = dt.AddDays(1))
            {
                bool isDstNew = AppTimeZone.IsDaylightSavingTime(dt);
                EndDateDTOld = dt;
                if (isDstNew != isDstOld)
                {
                    Console.WriteLine($"Daylight saving time change detected at {dt:dd/MM/yyyy HH:mm:ss}");
                    dstChange = true;
                    break;
                }
                isDstOld = isDstNew;
            }
            string StartDate = StartDateDT.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            string EndDate = EndDateDTOld.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            if (StartDate == EndDate)
            {
                StartDate = StartDateDT.AddDays(-1).ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
                EndDate = EndDateDT.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            }
            if (DateTime.Parse(EndDate, null, System.Globalization.DateTimeStyles.AdjustToUniversal) > DateTime.UtcNow)
            {
                EndDate = DateTime.UtcNow.AddMinutes(-10).ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            }
            Console.WriteLine("Final query date range: {0} to {1}", StartDate, EndDate);
            // FromDate should already be UTC, but ensure it has the correct Kind
            GCAdherenceData.AdherenceLastUpdate = DateTime.SpecifyKind(FromDate, DateTimeKind.Utc);
            DataSet AdherenceData = GCAdherenceData.GetAdherenceDataFromGC(StartDate, EndDate, dstChange);
            if (GCAdherenceData.Errors == true)
            {
                return null;
            }
            if (AdherenceData != null)
            {
                Console.WriteLine("Latest adherence data timestamp: {0}", GCAdherenceData.AdherenceLastUpdate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
                if (GCAdherenceData.AdherenceLastUpdate > AdherenceLastUpdate)
                {
                    if (dstChange)
                    {
                        AdherenceLastUpdate = DateTime.Parse(EndDate).AddDays(TotalLookBackDays);
                    }
                    else
                    {
                        AdherenceLastUpdate = DateTime.Parse(EndDate);
                    }
                }
            }
            return AdherenceData;
        }

        public DataSet WFMScheduleData()
        {
            BUData GCWFMScheduleData = new BUData();
            GCWFMScheduleData.TimeZoneConfig = TimeZoneConfig;
            GCWFMScheduleData.Initialize();
            // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
            string StartDate = DateToSyncFrom.ToString("yyyy-MM-ddTHH:00:00.000Z");

            _logger?.LogDebug("WFM Schedule start date: {Date}", StartDate);

            // Fix: Use full precision for end date to avoid data loss
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
            }
            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");

            DateTime CurrentDate = DateTime.UtcNow;
            DateTime CompareDate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
            DataSet DSTemp = new DataSet();
            if (CompareDate > CurrentDate)
            {
                _logger?.LogDebug("Date is in the future. Backdating to current time.");
                StartDate = CurrentDate.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            }
            if ((DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal) - DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal)).TotalDays >= 30)
            {
                _logger?.LogDebug("Over 30 days to sync: Processing in blocks");

                foreach (DateTime ProcessDay in EachDay(DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal), DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal)))
                {
                    // ProcessDay is already UTC from parsing, AddDays preserves the kind, use ToUtcSafe to avoid double conversion
                    DateTime ProcessTo = ProcessDay.AddDays(WFMDaysMaxProcess).ToUtcSafe();
                    if (ProcessDay > CompareDate)
                        break;
                    DSTemp.Merge(GCWFMScheduleData.GetScheduleDataFromGC(ProcessDay.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"), ProcessTo.ToString("yyyy-MM-ddTHH:mm:ss.fffZ")));

                    _logger?.LogDebug("Processing block: {From} to {To}, compare date: {CompareDate}",
                        ProcessDay.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                        ProcessTo.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                        CompareDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
                }
            }
            else
            {
                DSTemp = GCWFMScheduleData.GetScheduleDataFromGC(StartDate, EndDate);
            }
            WFMScheduleLastUpdate = GCWFMScheduleData.WFMScheduleLastUpdate;
            return DSTemp;
        }

        /// <summary>
        /// Gets user interaction data from Genesys Cloud.
        /// </summary>
        /// <param name="granularity">Optional granularity in minutes. If not provided, uses the default AggInterval from configuration.</param>
        /// <returns>A DataTable containing user interaction data.</returns>
        public DataTable UserInteractionData(string granularity = null)
        {
            UserData GCUserInteractionData = new UserData();
            GCUserInteractionData.TimeZoneConfig = TimeZoneConfig;
            GCUserInteractionData.AggInterval = AggInterval;
            GCUserInteractionData.Initialize();

            // Determine the actual start date for this entire operation
            DateTime overallStartDate = DateToSyncFrom.Subtract(LookBackSpan);
            DateTime overallEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            DateTime currentProcessingStartDate = overallStartDate;

            // Start with the original MaxSpanToSync
            TimeSpan currentSpan = MaxSpanToSync;
            DataTable accumulatedData = null;
            const int maxRetries = 10; // Maximum number of retries
            DateTime lastSuccessfulEndDate = overallStartDate; // Track the last successfully processed end date

            _logger?.LogDebug("UserInteraction:Start: Processing user interaction data from {StartDate} to {EndDate}",
                overallStartDate.ToString("yyyy-MM-ddTHH:00:00.000Z"),
                overallEndDate.ToString("yyyy-MM-ddTHH:00:00.000Z"));

            while (currentProcessingStartDate < overallEndDate)
            {
                bool success = false;
                int retryCount = 0;
                TimeSpan effectiveSpanForAttempt = TimeSpan.FromTicks(Math.Min(currentSpan.Ticks, (overallEndDate - currentProcessingStartDate).Ticks));

                while (!success && retryCount < maxRetries)
                {
                    try
                    {
                        string startDateString = currentProcessingStartDate.ToString("yyyy-MM-ddTHH:00:00.000Z");
                        DateTime currentAttemptEndDate = currentProcessingStartDate.Add(effectiveSpanForAttempt);
                        string endDateString = currentAttemptEndDate.ToString("yyyy-MM-ddTHH:00:00.000Z");

                        _logger?.LogDebug("UserInteraction:Attempt: Retrieving data with span: {Span:F1} hours, from {StartDate} to {EndDate}",
                            effectiveSpanForAttempt.TotalHours, startDateString, endDateString);

                        GCUserInteractionData.UserInteractionLastUpdate = DateTime.ParseExact(startDateString, "yyyy-MM-ddTHH:mm:ss.fffZ", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AdjustToUniversal);
                        DataTable currentBatchData = GCUserInteractionData.GetUserInteractionDataFromGC(startDateString, endDateString, UserAggViews, granularity);

                        if (accumulatedData == null)
                        {
                            accumulatedData = currentBatchData;
                        }
                        else if (currentBatchData != null)
                        {
                            accumulatedData.Merge(currentBatchData);
                        }

                        success = true;
                        lastSuccessfulEndDate = currentAttemptEndDate; // Track the last successful end date

                        // IMPORTANT: Override the UserInteractionLastUpdate with the processing window end date
                        // The GetUserInteractionDataFromGC method sets this to the actual data timestamps,
                        // but we need it to be the end of the processing window to advance the sync properly
                        DateTime dataTimestampFromAPI = GCUserInteractionData.UserInteractionLastUpdate;

                        _logger?.LogDebug("UserInteraction:Success: Retrieved data for span: {Span:F1} hours, from {StartDate} to {EndDate}. Data timestamps from API: {DataTimestamp}, Processing window end: {WindowEnd}",
                            effectiveSpanForAttempt.TotalHours, startDateString, endDateString,
                            dataTimestampFromAPI.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"),
                            currentAttemptEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ"));
                        currentProcessingStartDate = currentAttemptEndDate; // Move to the next window
                        currentSpan = MaxSpanToSync; // Reset span for next major window
                    }
                    catch (Exception ex) when (ex.Message.Contains("Result set is larger than result limit") ||
                                              ex.Message.Contains("BadRequest") ||
                                              ex.Message.Contains("JSON Deserialization Error"))
                    {
                        retryCount++;
                        effectiveSpanForAttempt = TimeSpan.FromHours(effectiveSpanForAttempt.TotalHours / 2);
                        _logger?.LogWarning("UserInteraction:Retry: API limit exceeded. Reducing time span to {Span:F1} hours and retrying. (Attempt {Attempt}/{MaxRetries})",
                            effectiveSpanForAttempt.TotalHours, retryCount, maxRetries);

                        if (effectiveSpanForAttempt.TotalHours < 1)
                        {
                            string errorMessage = $"Time span reduced to less than 1 hour ({effectiveSpanForAttempt.TotalHours:F1} hours) after {retryCount} attempts for window starting {currentProcessingStartDate:yyyy-MM-ddTHH:00:00.000Z}. There might be an issue with query parameters or persistent API errors. Last error: {ex.Message}";
                            _logger?.LogError("UserInteraction:Error: {ErrorMessage}", errorMessage);
                            throw new InvalidOperationException(errorMessage, ex);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger?.LogError(ex, "UserInteraction:Error: Unexpected error retrieving user interaction data for window starting {StartDate}",
                            currentProcessingStartDate.ToString("yyyy-MM-ddTHH:00:00.000Z"));
                        throw; // Rethrowing the original exception to preserve stack trace and type
                    }
                }

                if (!success)
                {
                    string finalErrorMessage = $"Failed to retrieve user interaction data for window starting {currentProcessingStartDate:yyyy-MM-ddTHH:00:00.000Z} after {maxRetries} attempts. The final attempted span was {effectiveSpanForAttempt.TotalHours:F1} hours.";
                    _logger?.LogError("UserInteraction:Error: {ErrorMessage}", finalErrorMessage);
                    throw new InvalidOperationException(finalErrorMessage);
                }
            }

            // Set UserInteractionLastUpdate to the last successfully processed end date
            // This ensures we advance the sync window to the end of the processing period,
            // not just to the timestamp of the last data record (which could be older)
            UserInteractionLastUpdate = lastSuccessfulEndDate;
            _logger?.LogInformation("UserInteraction:Complete: Finished processing user interaction data. Overall period covered: {StartDate} to {EndDate}. MaxSyncDate will be set to: {MaxSyncDate}",
                overallStartDate.ToString("yyyy-MM-ddTHH:00:00.000Z"),
                lastSuccessfulEndDate.ToString("yyyy-MM-ddTHH:00:00.000Z"),
                UserInteractionLastUpdate.ToString("yyyy-MM-ddTHH:00:00.000Z"));

            return accumulatedData;
        }

        /// <summary>
        /// Gets queue interaction data from Genesys Cloud.
        /// </summary>
        /// <param name="granularity">Optional granularity in minutes. If not provided, uses the default AggInterval from configuration.</param>
        /// <returns>A DataTable containing queue interaction data.</returns>
        public DataTable QueueInteractionData(string granularity = null)
        {
            QueueData GCQueueInteractionData = new QueueData();
            GCQueueInteractionData.AggInterval = AggInterval;
            GCQueueInteractionData.TimeZoneConfig = TimeZoneConfig;
            GCQueueInteractionData.Initialize();

            // Start with the original MaxSpanToSync
            TimeSpan currentSpan = MaxSpanToSync;
            DataTable QueueInteractionData = null;
            bool success = false;
            int retryCount = 0;
            const int maxRetries = 5; // Maximum number of retries

            while (!success && retryCount < maxRetries)
            {
                try
                {
                    string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
                    string EndDate = DateToSyncFrom.Add(currentSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");

                    Console.WriteLine($"Attempting to retrieve queue interaction data with span: {currentSpan.TotalHours:F1} hours");
                    Console.WriteLine($"Date range: {StartDate} to {EndDate}");

                    GCQueueInteractionData.QueueInteractionLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);

                    QueueInteractionData = GCQueueInteractionData.GetQueueInteractionDataFromGC(StartDate, EndDate, QueueAggViews, granularity);

                    // If we get here, the call succeeded
                    success = true;
                    QueueInteractionLastUpdate = DateToSyncFrom.Add(currentSpan);
                    Console.WriteLine($"Successfully retrieved queue interaction data with span: {currentSpan.TotalHours:F1} hours");
                }
                catch (Exception ex) when (ex.Message.Contains("Result set is larger than result limit") ||
                                          ex.Message.Contains("BadRequest") ||
                                          ex.Message.Contains("JSON Deserialization Error"))
                {
                    retryCount++;
                    // Reduce the time span by half for the next attempt
                    currentSpan = TimeSpan.FromHours(currentSpan.TotalHours / 2);
                    Console.WriteLine($"API limit exceeded. Reducing time span to {currentSpan.TotalHours:F1} hours and retrying. (Attempt {retryCount}/{maxRetries})");

                    // If we've reduced the span to less than 1 hour, something else might be wrong
                    if (currentSpan.TotalHours < 1)
                    {
                        Console.WriteLine("Time span reduced to less than 1 hour. There may be an issue with the query parameters.");
                        throw new Exception("Unable to retrieve queue interaction data even with minimal time span.", ex);
                    }
                }
                catch (Exception ex)
                {
                    // For other exceptions, just rethrow
                    Console.WriteLine($"Unexpected error retrieving queue interaction data: {ex.Message}");
                    throw;
                }
            }

            if (!success)
            {
                throw new Exception($"Failed to retrieve queue interaction data after {maxRetries} attempts with progressively smaller time spans.");
            }

            return QueueInteractionData;
        }

        public async Task<DataTable> QueueAuditData()
        {
            _logger?.LogInformation("Starting Queue User Membership Audit");
            QueueData GCQueueAuditData = new QueueData(_logger);
            GCQueueAuditData.TimeZoneConfig = TimeZoneConfig;
            GCQueueAuditData.Initialize();
            // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            // Fix: Use full precision for end date to avoid data loss
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
            }
            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            GCQueueAuditData.QueueInteractionLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
            DataTable QueueQueueUserAuditData = await GCQueueAuditData.GetQueueAuditData(StartDate, EndDate);
            if (QueueQueueUserAuditData != null)
            {
                QueueUserAuditLastUpdate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
                if (QueueUserAuditLastUpdate > DateTime.UtcNow)
                    QueueUserAuditLastUpdate = DateTime.UtcNow.AddHours(-2);

                _logger?.LogDebug("API call successful. Updating last sync date to {LastSyncDate}. Rows retrieved: {RowCount}",
                    QueueUserAuditLastUpdate, QueueQueueUserAuditData.Rows.Count);
            }
            else
            {
                _logger?.LogWarning("API call failed. Last sync date will not be updated.");
            }
            return QueueQueueUserAuditData;
        }

        public DataTable ShrinkageData()
        {

            _logger?.LogInformation("Starting Shrinkage Data job");
            ShrinkageData GCShrinkageData = new ShrinkageData(_logger);
            GCShrinkageData.TimeZoneConfig = TimeZoneConfig;
            GCShrinkageData.Initialize();
            // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            // Fix: Use full precision for end date to avoid data loss
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
            }
            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            GCShrinkageData.ShrinkageLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
            DataTable ShrinkageData = GCShrinkageData.GetShrinkageData(StartDate, EndDate);
            if (ShrinkageData != null)
            {
                // API call was successful, update the last sync date
                ShrinkageLastUpdate = DateToSyncFrom.Add(MaxSpanToSync);
                _logger?.LogDebug("API call successful. Updating last sync date to {LastSyncDate}. Rows retrieved: {RowCount}",
                    ShrinkageLastUpdate, ShrinkageData.Rows.Count);
            }
            else
            {
                // API call failed, don't update the last sync date
                _logger?.LogWarning("API call failed. Last sync date will not be updated.");
            }
            return ShrinkageData;
        }

        public DataTable WFMAuditData()
        {
            _logger?.LogInformation("Starting WFM Changes Audit");
            WFMAuditData GCWFMAuditData = new WFMAuditData(_logger);
            GCWFMAuditData.TimeZoneConfig = TimeZoneConfig;
            GCWFMAuditData.Initialize();
            // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            // Fix: Use full precision for end date to avoid data loss
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
            }
            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            GCWFMAuditData.WFMAuditLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
            DataTable WFMAuditData = GCWFMAuditData.GetWFMAuditData(StartDate, EndDate);
            if (WFMAuditData != null)
            {
                QueueUserAuditLastUpdate = DateTime.ParseExact(EndDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
                if (QueueUserAuditLastUpdate > DateTime.UtcNow)
                    QueueUserAuditLastUpdate = DateTime.UtcNow.AddHours(-2);

                _logger?.LogDebug("API call successful. Updating last sync date to {LastSyncDate}. Rows retrieved: {RowCount}",
                    QueueUserAuditLastUpdate, WFMAuditData.Rows.Count);
            }
            else
            {
                _logger?.LogWarning("API call failed. Last sync date will not be updated.");
            }
            return WFMAuditData;
        }

        public async Task<DataSet> DetailInteractionData(CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames, CSG.Adapter.Configuration.RateLimiting? rateLimitingConfig = null)
        {
            DataSet DetailInteractionData = new DataSet();
            DetailData GCDetailInteractionData = new DetailData(_logger, renameParticipantAttributeNames, rateLimitingConfig);
            GCDetailInteractionData.TimeZoneConfig = TimeZoneConfig;

            // Clear processed conversation tracking at the start of each job run to prevent cross-job duplicate detection
            GCDetailInteractionData.ClearProcessedConversationTracking();
            TimeSpan adjustedLookBackSpan = Math.Abs(LookBackSpan.TotalHours) == 2 ? TimeSpan.FromHours(4) : LookBackSpan;
            // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
            string StartDate = DateToSyncFrom.Subtract(adjustedLookBackSpan).ToString("yyyy-MM-ddTHH:mm:00.000Z");
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DateTime originalEndDate = DTEndDate;
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
                _logger?.LogDebug("Adjusted end date from {OriginalDate} to {AdjustedDate} (current time constraint)",
                    originalEndDate, DTEndDate);
            }
            // Fix: Use full precision timestamp instead of truncating to hour boundary
            // This ensures we capture all available data up to the exact end time
            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            GCDetailInteractionData.DetailInteractionLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
            DateTime FromDate = DateToSyncFrom.Add(MaxSpanToSync);
            DateTime ToDate = DateTime.UtcNow; // DateTime.UtcNow is already UTC, no need for .ToUniversalTime()
            TimeSpan DateDiff = ToDate - FromDate;
            double MinutesDifference = DateDiff.TotalMinutes;



            // Condensed logging: Show sync method and key parameters in single log
            string syncMethod = MinutesDifference < 1440 ? "QUERY" : "JOB";
            _logger?.LogInformation("DetailedInteraction: Using {SyncMethod} | Span: {DateDiff} | Range: {StartDate} to {EndDate}",
                syncMethod, DateDiff, StartDate, DTEndDate.ToString("yyyy-MM-ddTHH:mm:00.000Z"));

            if (MinutesDifference < 1440)
            {
                DetailInteractionData = GCDetailInteractionData.GetDetailInteractionDataFromGC("QUERY", StartDate, EndDate, adjustedLookBackSpan);
            }
            else
            {
                DetailInteractionData = GCDetailInteractionData.GetDetailInteractionDataFromGC("JOB", StartDate, EndDate, adjustedLookBackSpan);
            }
            if (DetailInteractionData != null)
            {
                if (DTEndDate > DateTime.UtcNow)
                {
                    DetailInteractionLastUpdate = DateTime.UtcNow;
                }
                else
                {
                    DetailInteractionLastUpdate = DTEndDate;
                }


            }
            return DetailInteractionData;
        }

        /// <summary>
        /// Synchronous entry point for voice analysis. Internally, this method calls the asynchronous VoiceAnalysisCombinedDataAsync.
        /// </summary>
        public DataSet VoiceAnalysisData(DataTable Conversations)
        {
            return VoiceAnalysisCombinedDataAsync(Conversations).GetAwaiter().GetResult();
        }

        /// <summary>
        /// Retrieves both overview and detail voice analysis data for the provided conversation DataTable.
        /// </summary>
        /// <param name="Conversations">A DataTable containing conversation records.</param>
        /// <returns>A DataSet containing voice analysis data.</returns>
        public async Task<DataSet> VoiceAnalysisCombinedDataAsync(DataTable Conversations)
        {
            // Initialize VoiceAnalysis with the parent logger to avoid duplicate logs
            // Create a logger factory to create a typed logger
            ILogger<VoiceAnalysis> typedLogger = _logger != null ?
                LoggerFactoryExtensions.CreateLogger<VoiceAnalysis>(new LoggerFactory()) : null;
            VoiceAnalysis voiceAnalysis = new VoiceAnalysis(typedLogger);
            voiceAnalysis.TimeZoneConfig = this.TimeZoneConfig;

            // Use async initialization to prevent concurrent authentication requests
            await voiceAnalysis.InitializeAsync();

            // Call the asynchronous voice analysis method
            DataSet voiceAnalysisData = await voiceAnalysis.VoiceAnalysisCombinedDataAsync(Conversations);

            // Log at debug level to reduce log volume in multi-threaded scenarios
            _logger?.LogDebug("Voice:Batch: Processed batch with {Count} conversations, returned {TableCount} tables",
                Conversations.Rows.Count, voiceAnalysisData?.Tables?.Count ?? 0);

            return voiceAnalysisData;
        }

        public DataSet EvaluationDetailedData()
        {
            DataSet EvaluationDetailsSet = new DataSet();
            EvalData GCEvaluationData = new EvalData();
            GCEvaluationData.TimeZoneConfig = TimeZoneConfig;
            GCEvaluationData.Initialize();
            // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            // Fix: Use full precision for end date to avoid data loss
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
            }
            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");

            _logger?.LogDebug("Evaluation sync parameters: Date={Date}, LookBackSpan={LookBack}, MaxSyncSpan={MaxSync}, StartDate={Start}, EndDate={End}",
                DateToSyncFrom.ToString("yyyy-MM-ddTHH:00:00.000Z"),
                LookBackSpan,
                MaxSpanToSync,
                StartDate,
                EndDate);
            GCEvaluationData.DetailEvaluationLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
            EvaluationDetailsSet = GCEvaluationData.GetEvalDetailsFromGC(StartDate, EndDate);
            DetailEvaluationLastUpdate = DateToSyncFrom.Add(MaxSpanToSync);

            _logger?.LogDebug("Evaluation last update: {LastUpdate}", DetailEvaluationLastUpdate);

            return EvaluationDetailsSet;
        }

        public DataSet EvalDataCatchUp(DataTable PendingEvals)
        {
            DataSet EvaluationDetailsSet = new DataSet();
            EvalData GCEvaluationData = new EvalData();
            GCEvaluationData.TimeZoneConfig = TimeZoneConfig;
            GCEvaluationData.Initialize();
            // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            string EndDate = DateToSyncFrom.Add(MaxSpanToSync).ToString("yyyy-MM-ddTHH:00:00.000Z");
            EvaluationDetailsSet = GCEvaluationData.GetEvaluationDetailsFromGC(PendingEvals, StartDate, EndDate);
            return EvaluationDetailsSet;
        }

        public DataTable ChatData(CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames)
        {
            ChatData GCChatData = new ChatData(_logger);
            DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
            DBUtil.Initialize();
            GCChatData.TimeZoneConfig = TimeZoneConfig;

            DataTable Users = DBUtil.GetSQLTableData("select * from userdetails", "userdetails");
            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            GCChatData.DetailChatLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
            // Fix: Use full precision for end date to avoid data loss
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
            }
            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            DataTable ChatData = GCChatData.CalculateChatData(StartDate, EndDate, Users, renameParticipantAttributeNames, true);
            if (GCChatData.DetailChatLastUpdate > DetailChatLastUpdate)
                DetailChatLastUpdate = GCChatData.DetailChatLastUpdate;

            return ChatData;
        }

        public DataTable MessageData(CSG.Adapter.Configuration.RegexReplacement[]? renameParticipantAttributeNames)
        {
            MessageData GCMessageData = new MessageData(_logger);
            DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
            DBUtil.Initialize();
            GCMessageData.TimeZoneConfig = TimeZoneConfig;
            // DateToSyncFrom is already UTC from GetSyncLastUpdate, no need to convert
            string StartDate = DateToSyncFrom.Subtract(LookBackSpan).ToString("yyyy-MM-ddTHH:00:00.000Z");
            // Fix: Use full precision for end date to avoid data loss
            DateTime DTEndDate = DateToSyncFrom.Add(MaxSpanToSync);
            if (DTEndDate > DateTime.UtcNow)
            {
                DTEndDate = DateTime.UtcNow.AddSeconds(-30);
            }
            string EndDate = DTEndDate.ToString("yyyy-MM-ddTHH:mm:ss.fffZ");
            // Parse with DateTimeStyles.AdjustToUniversal to ensure proper UTC handling
            GCMessageData.DetailMessageLastUpdate = DateTime.ParseExact(StartDate, "yyyy-MM-ddTHH:mm:ss.fffZ", null, System.Globalization.DateTimeStyles.AdjustToUniversal);
            DataTable UserConfig = DBUtil.GetSQLTableData("Select * from userDetails", "userDetails");
            // Use chatdata table structure with mediatype='message' for unified storage
            DataTable DetailMessageData = GCMessageData.CalculateMessageDataForChatTable(StartDate, EndDate, UserConfig, renameParticipantAttributeNames, ForSQL: true);
            if (DetailMessageData != null)
            {
                if (GCMessageData.DetailMessageLastUpdate > DetailMessageLastUpdate)
                    DetailMessageLastUpdate = GCMessageData.DetailMessageLastUpdate;
            }
            return DetailMessageData;
        }

        public DataTable UserDetailsFromDB()
        {
            DBUtils.DBUtils DBAdapter = new DBUtils.DBUtils();
            DataTable DTUser = new DataTable();
            try
            {
                DTUser = DBAdapter.GetSQLTableData("select * from userRealTimeData", "userRealTimeData");
            }
            catch (Exception ex)
            {
                _logger?.LogWarning(ex, "Suppressed error getting user details from database");
            }
            finally
            {
                DBAdapter = null;
            }
            return DTUser;
        }

        public DataTable LearningAssignmentResultsData()
        {
            try
            {
                DataTable LearningModuleAssignments = _dbConnector.GetSQLTableData("select * from learningmoduleassignments", "learningmoduleassignments");

                // Check if we have any assignments to process
                if (LearningModuleAssignments == null || LearningModuleAssignments.Rows.Count == 0)
                {
                    _logger?.LogInformation("No learning module assignments found in database to process");
                    return new DataTable("learningassignmentresults");
                }

                LearningDataConfig LearningData = new LearningDataConfig(_logger);
                LearningData.Initialize();
                DataTable LearningAssignmentResultsData = LearningData.GetLearningAssignmentResultsFromGC(LearningModuleAssignments);
                return LearningAssignmentResultsData;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in LearningAssignmentResultsData: {ExceptionType}: {Message}",
                    ex.GetType().Name, ex.Message);
                // Return empty table instead of null to prevent further exceptions
                return new DataTable("learningassignmentresults");
            }
        }

        public DataTable KnowledgeBaseDocumentData()
        {
            DataTable KnowledgeBaseDetails = _dbConnector.GetSQLTableData("select * from knowledgeBase", "knowledgeBase");
            KnowledgeBaseConfig KnowledgeBase = new KnowledgeBaseConfig();
            KnowledgeBase.Initialize();
            DataTable KnowledgeBaseDocumentData = KnowledgeBase.GetKnowledgeBaseDocumentDataFromGC(KnowledgeBaseDetails);
            return KnowledgeBaseDocumentData;
        }

        public Boolean UpdatePermissionsFromFileGC(string clientId, string filePath)
        {
            Boolean Successful = false;

            var permissionConfig = new PermissionConfig(_logger);

            // Construct the JObject for preferences with proper structure
            JObject preferences = new JObject
            {
                ["Permissions"] = new JObject
                {
                    ["Update"] = true, // Add Update permission
                    ["ForcedUpdate"] = _enableForcedUpdatePermissions // Add ForcedUpdate permission
                }
            };

            // Pass the constructed preferences object to Initialize
            permissionConfig.Initialize(preferences);

            try
            {
                // Update permissions by reading from the JSON file
                permissionConfig.UpdateRolePermissionsFromFile(clientId, filePath);
                Successful = true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to update role permissions from file.");
                Successful = false;
            }

            return Successful;
        }

#nullable enable
        public bool UpdateLastSuccessDate(DateTime LastSuccessDate, string Key)
        {
            bool Successful = false;
            Successful = _dbConnector.SetSyncLastUpdate(LastSuccessDate, Key);
            return Successful;
        }
#nullable restore

        private static IEnumerable<DateTime> EachDay(DateTime from, DateTime to)
        {
            for (var day = from.Date; day.Date <= to.Date; day = day.AddDays(WFMDaysMaxProcess))
                yield return day;
        }


    }
}
