﻿-- Function to update mediatype column in chatdata table
-- This function identifies the media type (chat vs message) for conversations in chatdata
-- by querying only the relevant conversationids from detailedinteractiondata table
-- Performance optimized: only queries detailedinteractiondata for existing chatdata records

CREATE OR REPLACE FUNCTION update_chatdata_mediatype()
RETURNS TABLE(
    mediatype VARCHAR(10),
    record_count BIGINT
) AS $$
DECLARE
    updated_rows INTEGER := 0;
    start_time TIMESTAMP := NOW();
    end_time TIMESTAMP;
    duration_seconds INTEGER;
BEGIN
    RAISE NOTICE 'Starting optimized mediatype update for chatdata table at %', start_time;
    RAISE NOTICE 'Only querying detailedinteractiondata for conversations that exist in chatdata with NULL mediatype';

    -- Update mediatype for records where it's currently NULL
    -- Only query detailedinteractiondata for conversations that exist in chatdata
    WITH conversation_media AS (
        SELECT DISTINCT ON (d.conversationid)
            d.conversationid,
            d.mediatype
        FROM detailedinteractiondata d
        INNER JOIN chatdata c_filter ON d.conversationid = c_filter.conversationid
        WHERE d.mediatype IN ('chat', 'message')
        AND c_filter.mediatype IS NULL
        ORDER BY d.conversationid, d.conversationstartdate DESC
    )
    UPDATE chatdata c
    SET mediatype = CASE
        WHEN cm.mediatype = 'chat' THEN 'chat'
        WHEN cm.mediatype = 'message' THEN 'message'
        ELSE 'unknown'
    END
    FROM conversation_media cm
    WHERE c.conversationid = cm.conversationid
    AND c.mediatype IS NULL;

    GET DIAGNOSTICS updated_rows = ROW_COUNT;

    end_time := NOW();
    duration_seconds := EXTRACT(EPOCH FROM (end_time - start_time))::INTEGER;

    RAISE NOTICE 'Completed mediatype update. Updated % rows in % seconds.', updated_rows, duration_seconds;

    -- Return summary of current mediatype distribution
    RETURN QUERY
    SELECT
        COALESCE(c.mediatype, 'NULL') as mediatype,
        COUNT(*) as record_count
    FROM chatdata c
    GROUP BY c.mediatype
    ORDER BY c.mediatype;

END;
$$ LANGUAGE plpgsql;

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION update_chatdata_mediatype() TO PUBLIC;
