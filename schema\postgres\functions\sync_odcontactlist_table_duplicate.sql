DELETE FROM odcontactlistdata
WHERE EXISTS (
    SELECT 1
    FROM tabledefinitions
    WHERE tablename = 'odcontactlistdata'
      AND version LIKE '3.49.%'
      AND version < '3.49.2'
)
AND "inin-outbound-id" IN (
    SELECT "inin-outbound-id"
    FROM (
        SELECT "inin-outbound-id",
               ROW_NUMBER() OVER (
                   PARTITION BY "inin-outbound-id"
                   ORDER BY updated ASC
               ) AS rn
        FROM odcontactlistdata
        WHERE "inin-outbound-id" IN (
            SELECT "inin-outbound-id"
            FROM odcontactlistdata
            GROUP BY "inin-outbound-id"
            HAVING COUNT(DISTINCT contactlistid) > 1
        )
    ) ranked_rows
    WHERE rn = 1
);
