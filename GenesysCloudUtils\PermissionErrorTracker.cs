﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using Microsoft.Extensions.Logging;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Tracks permission errors (HTTP 403 Forbidden) to differentiate between temporary and permanent issues.
    /// </summary>
    public class PermissionErrorTracker
    {
        private static readonly ConcurrentDictionary<string, int> _errorCounts = new ConcurrentDictionary<string, int>();
        private static readonly ConcurrentDictionary<string, DateTime> _firstErrorTimes = new ConcurrentDictionary<string, DateTime>();
        private static readonly object _lockObject = new object();
        private readonly ILogger? _logger;

        /// <summary>
        /// The maximum number of 403 errors allowed for a resource before considering it a permanent issue.
        /// </summary>
        public int MaxErrorThreshold { get; set; } = 3;

        /// <summary>
        /// The time window in minutes within which errors are counted towards the threshold.
        /// </summary>
        public int ErrorWindowMinutes { get; set; } = 30;

        public PermissionErrorTracker(ILogger? logger = null)
        {
            _logger = logger;
        }

        /// <summary>
        /// Records a permission error for the specified resource and determines if it should be treated as a permanent issue.
        /// </summary>
        /// <param name="resourceKey">A unique identifier for the resource (typically the URL or endpoint)</param>
        /// <param name="errorMessage">The error message associated with the permission error</param>
        /// <returns>True if the error should be treated as permanent, false if it should be treated as temporary</returns>
        public bool RecordError(string resourceKey, string errorMessage)
        {
            lock (_lockObject)
            {
                // Record the first error time if this is the first error for this resource
                if (!_firstErrorTimes.ContainsKey(resourceKey))
                {
                    _firstErrorTimes[resourceKey] = DateTime.UtcNow;
                }

                // Check if we're outside the error window and should reset
                DateTime firstErrorTime = _firstErrorTimes[resourceKey];
                if ((DateTime.UtcNow - firstErrorTime).TotalMinutes > ErrorWindowMinutes)
                {
                    _errorCounts.TryRemove(resourceKey, out _);
                    _firstErrorTimes[resourceKey] = DateTime.UtcNow;
                }

                // Increment the error count
                int currentCount = _errorCounts.AddOrUpdate(
                    resourceKey,
                    1,
                    (key, oldValue) => oldValue + 1
                );

                _logger?.LogWarning(
                    "Permission error ({Count}/{Threshold}) for resource {Resource}: {Message}",
                    currentCount,
                    MaxErrorThreshold,
                    resourceKey,
                    errorMessage
                );

                // Determine if this should be treated as a permanent error
                bool isPermanent = currentCount >= MaxErrorThreshold;

                if (isPermanent)
                {
                    _logger?.LogError(
                        "Exceeded permission error threshold ({Threshold}) for resource {Resource}. Treating as permanent permission issue.",
                        MaxErrorThreshold,
                        resourceKey
                    );
                }
                else
                {
                    _logger?.LogWarning(
                        "Treating permission error as temporary for resource {Resource}. Will retry operation.",
                        resourceKey
                    );
                }

                return isPermanent;
            }
        }

        /// <summary>
        /// Resets the error count for a specific resource.
        /// </summary>
        /// <param name="resourceKey">The resource key to reset</param>
        public void ResetErrorCount(string resourceKey)
        {
            _errorCounts.TryRemove(resourceKey, out _);
            _firstErrorTimes.TryRemove(resourceKey, out _);
        }

        /// <summary>
        /// Resets all error counts.
        /// </summary>
        public void ResetAllErrorCounts()
        {
            _errorCounts.Clear();
            _firstErrorTimes.Clear();
        }
    }
}
