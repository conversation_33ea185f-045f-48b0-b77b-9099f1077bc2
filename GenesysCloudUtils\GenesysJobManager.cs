﻿using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace GenesysCloudUtils
{
    /// <summary>
    /// Centralized job management framework for Genesys Cloud asynchronous jobs
    /// Provides unified polling, error handling, and result retrieval across all job types
    /// </summary>
    public class GenesysJobManager
    {
        private readonly JsonUtils _jsonActions;
        private readonly string _gcApiKey;
        private readonly string _baseUri;

        /// <summary>
        /// Initializes a new instance of the GenesysJobManager
        /// </summary>
        /// <param name="jsonActions">JsonUtils instance for API calls</param>
        /// <param name="gcApiKey">Genesys Cloud API key</param>
        /// <param name="baseUri">Base URI for Genesys Cloud API</param>
        public GenesysJobManager(JsonUtils jsonActions, string gcApiKey, string baseUri)
        {
            _jsonActions = jsonActions ?? throw new ArgumentNullException(nameof(jsonActions));
            _gcApiKey = gcApiKey ?? throw new ArgumentNullException(nameof(gcApiKey));
            _baseUri = baseUri ?? throw new ArgumentNullException(nameof(baseUri));
        }

        /// <summary>
        /// Polls a job until completion with configurable parameters
        /// </summary>
        /// <param name="config">Job configuration including endpoints, timeouts, and status mappings</param>
        /// <returns>JobResult containing success status, error messages, and download URLs</returns>
        public JobResult PollJobToCompletion(JobConfig config)
        {
            var task = PollJobToCompletionAsync(config);
            return task.Result;
        }

        /// <summary>
        /// Asynchronously polls a job until completion with adaptive polling intervals
        /// </summary>
        /// <param name="config">Job configuration including endpoints, timeouts, and status mappings</param>
        /// <returns>JobResult containing success status, error messages, and download URLs</returns>
        public async Task<JobResult> PollJobToCompletionAsync(JobConfig config)
        {
            if (config == null)
                throw new ArgumentNullException(nameof(config));

            var result = new JobResult { JobId = config.JobId };
            DateTime startTime = DateTime.UtcNow;
            DateTime endTime = startTime.AddMinutes(config.TimeoutMinutes);

            // Adaptive polling: start with 2 seconds, exponential backoff up to 10 seconds
            int currentPollInterval = Math.Max(2, config.PollIntervalSeconds / 5); // Start faster
            int maxPollInterval = Math.Max(10, config.PollIntervalSeconds);

            Console.WriteLine("Polling {0} job {1} for {2} (adaptive intervals: {3}s to {4}s)",
                config.JobType, config.JobId, config.ContextInfo, currentPollInterval, maxPollInterval);

            while (DateTime.UtcNow < endTime)
            {
                try
                {
                    string jobStatusJson = _jsonActions.JsonReturnString(config.StatusEndpoint, _gcApiKey);

                    // Handle different response scenarios more accurately
                    if (jobStatusJson == null)
                    {
                        // null indicates rate limiting or task cancellation - should retry
                        Console.WriteLine("Rate limiting or temporary issue when polling {0} job {1} - retrying", config.JobType, config.JobId);
                        await Task.Delay(currentPollInterval * 1000);
                        continue;
                    }

                    if (string.IsNullOrWhiteSpace(jobStatusJson))
                    {
                        // Empty response during polling is normal - job is still processing
                        var elapsedTime = DateTime.UtcNow - startTime;
                        Console.WriteLine("{0} job {1} still processing (elapsed: {2:mm\\:ss}, next check in {3}s)",
                            config.JobType, config.JobId, elapsedTime, currentPollInterval);
                        await Task.Delay(currentPollInterval * 1000);
                        continue;
                    }

                    // Check for API errors (excluding rate limiting which is handled by JsonReturnString)
                    if (jobStatusJson.Contains("\"error\":") && !jobStatusJson.Contains("\"statusCode\":\"TooManyRequests\""))
                    {
                        // Check if this is a structured error response that should be retried
                        if (jobStatusJson.Contains("\"statusCode\":\"Forbidden\"") ||
                            jobStatusJson.Contains("\"statusCode\":\"RequestTimeout\"") ||
                            jobStatusJson.Contains("\"statusCode\":\"RequestCancelled\""))
                        {
                            Console.WriteLine("Temporary error polling {0} job {1}: {2} - retrying", config.JobType, config.JobId,
                                jobStatusJson.Length > 200 ? jobStatusJson.Substring(0, 200) + "..." : jobStatusJson);
                            await Task.Delay(currentPollInterval * 1000);
                            continue;
                        }

                        // For other errors, fail the job
                        Console.WriteLine("Error polling {0} job {1}: {2}", config.JobType, config.JobId,
                            jobStatusJson.Length > 300 ? jobStatusJson.Substring(0, 300) + "..." : jobStatusJson);
                        result.Success = false;
                        result.ErrorMessage = "API error during job polling";
                        return result;
                    }

                    // Parse job status using the provided parser
                    var jobStatus = config.StatusParser(jobStatusJson);
                    if (jobStatus == null)
                    {
                        Console.WriteLine("Failed to parse {0} job {1} status from response: {2}",
                            config.JobType, config.JobId,
                            jobStatusJson.Length > 100 ? jobStatusJson.Substring(0, 100) + "..." : jobStatusJson);
                        await Task.Delay(currentPollInterval * 1000);
                        continue;
                    }

                    // Calculate elapsed time for progress reporting
                    var elapsed = DateTime.UtcNow - startTime;
                    Console.WriteLine("{0} job {1} status: {2} (elapsed: {3:mm\\:ss}, next poll in {4}s)",
                        config.JobType, config.JobId, jobStatus.Status, elapsed, currentPollInterval);

                    // Check completion status
                    if (config.CompletedStatuses.Contains(jobStatus.Status.ToLower()))
                    {
                        Console.WriteLine("{0} job {1} completed successfully after {2:mm\\:ss}",
                            config.JobType, config.JobId, elapsed);
                        result.Success = true;
                        result.DownloadUrls = jobStatus.DownloadUrls ?? new List<string>();
                        result.JobData = jobStatus.JobData;
                        return result;
                    }

                    // Check failure status
                    if (config.FailedStatuses.Contains(jobStatus.Status.ToLower()))
                    {
                        Console.WriteLine("{0} job {1} failed with status: {2} after {3:mm\\:ss}",
                            config.JobType, config.JobId, jobStatus.Status, elapsed);
                        result.Success = false;
                        result.ErrorMessage = $"Job failed with status: {jobStatus.Status}";
                        return result;
                    }

                    // Job still processing - use adaptive polling interval
                    await Task.Delay(currentPollInterval * 1000);

                    // Increase poll interval for next iteration (exponential backoff)
                    currentPollInterval = Math.Min(currentPollInterval * 2, maxPollInterval);
                }
                catch (Exception ex)
                {
                    Console.WriteLine("Exception polling {0} job {1}: {2}", config.JobType, config.JobId, ex.Message);
                    await Task.Delay(currentPollInterval * 1000);

                    // Don't increase interval on exceptions, keep trying at current rate
                }
            }

            Console.WriteLine("Timeout waiting for {0} job {1} to complete", config.JobType, config.JobId);
            result.Success = false;
            result.ErrorMessage = "Job polling timeout";
            return result;
        }

        /// <summary>
        /// Extracts job information from HTTP 202 Accepted response wrapped by JsonReturnString
        /// </summary>
        /// <param name="wrappedResponse">The wrapped response from JsonReturnString</param>
        /// <returns>The extracted job JSON or empty string if extraction fails</returns>
        public static string ExtractJobFromAcceptedResponse(string wrappedResponse)
        {
            try
            {
                // The response format is: {"error": true, "message": "Accepted: {job data}", "statusCode": "Accepted"}
                // We need to extract the job data from the message field

                var responseObj = JsonConvert.DeserializeObject<dynamic>(wrappedResponse);
                if (responseObj?.message != null)
                {
                    string message = responseObj.message.ToString();
                    if (message.StartsWith("Accepted: "))
                    {
                        // Extract the JSON part after "Accepted: "
                        string jobJson = message.Substring("Accepted: ".Length);
                        return jobJson;
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error extracting job from Accepted response: {0}", ex.Message);
            }

            return string.Empty;
        }
    }

    /// <summary>
    /// Configuration for job polling operations
    /// </summary>
    public class JobConfig
    {
        /// <summary>
        /// Unique identifier for the job
        /// </summary>
        public string JobId { get; set; } = string.Empty;

        /// <summary>
        /// Type of job for logging purposes (e.g., "Adherence", "Analytics", "Segment")
        /// </summary>
        public string JobType { get; set; } = string.Empty;

        /// <summary>
        /// Context information for logging (e.g., management unit name)
        /// </summary>
        public string ContextInfo { get; set; } = string.Empty;

        /// <summary>
        /// Full endpoint URL for checking job status
        /// </summary>
        public string StatusEndpoint { get; set; } = string.Empty;

        /// <summary>
        /// Maximum time to wait for job completion in minutes
        /// </summary>
        public int TimeoutMinutes { get; set; } = 30;

        /// <summary>
        /// Interval between status checks in seconds
        /// </summary>
        public int PollIntervalSeconds { get; set; } = 10;

        /// <summary>
        /// List of status values that indicate successful completion (case-insensitive)
        /// </summary>
        public List<string> CompletedStatuses { get; set; } = new List<string>();

        /// <summary>
        /// List of status values that indicate job failure (case-insensitive)
        /// </summary>
        public List<string> FailedStatuses { get; set; } = new List<string>();

        /// <summary>
        /// Function to parse job status from API response JSON
        /// </summary>
        public Func<string, JobStatusInfo> StatusParser { get; set; } = null!;
    }

    /// <summary>
    /// Generic job status information extracted from API responses
    /// </summary>
    public class JobStatusInfo
    {
        /// <summary>
        /// Current status of the job
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// List of download URLs available when job completes
        /// </summary>
        public List<string> DownloadUrls { get; set; } = new List<string>();

        /// <summary>
        /// Additional job data that may be needed for processing
        /// </summary>
        public object? JobData { get; set; }
    }

    /// <summary>
    /// Result of job polling operation
    /// </summary>
    public class JobResult
    {
        /// <summary>
        /// Job identifier
        /// </summary>
        public string JobId { get; set; } = string.Empty;

        /// <summary>
        /// Whether the job completed successfully
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// Error message if job failed
        /// </summary>
        public string ErrorMessage { get; set; } = string.Empty;

        /// <summary>
        /// Download URLs available from completed job
        /// </summary>
        public List<string> DownloadUrls { get; set; } = new List<string>();

        /// <summary>
        /// Additional job data returned from the API
        /// </summary>
        public object? JobData { get; set; }
    }
}
