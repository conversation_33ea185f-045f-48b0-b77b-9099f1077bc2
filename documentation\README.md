# Genesys Adapter Documentation

This directory contains documentation for the Genesys Adapter.

## Documentation Structure

### Core Documentation
- [Database Documentation](database/README.md): Documentation for the database components of the Genesys Adapter, including schema documentation, database functions, and best practices.
- [Message Job Implementation](MESSAGE_JOB_IMPLEMENTATION.md): Documentation for the message job functionality and implementation details.

### CI/CD and Release Management
- [Automated Tagging](AUTOMATED_TAGGING.md): Documentation for the automated git tagging system in the CI pipeline.
- [Release Notes Generator](RELEASE_NOTES_GENERATOR.md): Documentation for the automated release notes generation system.

### Technical Documentation
- [Rate Limit Fixes Summary](RATE_LIMIT_FIXES_SUMMARY.md): Summary of rate limiting fixes and improvements.
- [Test Survey Fixes](test_survey_fixes.md): Documentation for survey-related test fixes and improvements.

## Development Guidelines

For development guidelines, please refer to the main [README.md](../README.md) file, which includes information on:

- Development practices
- Build instructions
- Testing procedures
- Deployment guidelines

## Contributing to Documentation

When contributing to the documentation:

1. Place documentation in the appropriate subdirectory based on the component it relates to.
2. Use Markdown format for all documentation files.
3. Include code examples where appropriate.
4. Keep documentation up-to-date with code changes.
5. Link related documentation files to provide a comprehensive view of the system.
