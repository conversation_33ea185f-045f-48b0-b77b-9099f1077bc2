﻿
using System.Data;
using System.Reflection;
using System.Text.RegularExpressions;
using CSG.Common.Exceptions;
using Microsoft.Extensions.Logging;
using StandardUtils;

public class Install
{
    private readonly ILogger _logger;



    public Install(ILogger logger)
    {
        _logger = logger;
    }

    public class SchemaFileComparer : IComparer<string>
    {
        public int Compare(string? x, string? y)
        {
            // Processed (in order) before any other file, regardless of type
            var filesFirst = new List<string> {
                "installfunctions.sql",  // Ensure installfunctions.sql is first
                "tabledefinitions.sql",
            };

            // Processed (in order) after any other file, regardless of type
            var filesLast = new List<string> {
                ".update_mvwevaluationgroupdata.sql",
                ".update_mvwConvVoiceOverviewData.sql",
                ".update_mvwConvVoiceSentimentDetailData.sql",
                ".update_mvwConvVoiceTopicDetailData.sql",
                ".partman_configure.sql",
                ".partman_install.sql",
            };

            // Sort by type of object the SQL is operating on - CRITICAL: tables must come before views
            var typeOrder = new List<string> {
                ".tables.",              // Tables must be created first
                ".functions.",           // Functions come next
                ".views.",               // Views come last as they depend on tables
            };

            // Sort to the top of the files in the type of object
            var filesFirstWithinType = new List<string> {
                ".vwUserDetail.sql",
                ".vwConvSummaryData.sql",
                ".vwDetailedInteractionData.sql",
                ".vwQueueDetails.sql",
                ".vwRealTimeUserConv.sql",
            };

            // Sort null values to the end
            if (x == null && y == null)
                return 0;
            else if (x == null)
                return 1;
            else if (y == null)
                return -1;

            // Files to run before any others regardless of type
            int xPriority = GetSubstringPriority(filesFirst, x);
            int yPriority = GetSubstringPriority(filesFirst, y);
            if (xPriority != yPriority)
                return xPriority.CompareTo(yPriority);

            // Files to run after any others regardless of type
            xPriority = GetSubstringPriority(filesLast, x);
            yPriority = GetSubstringPriority(filesLast, y);
            if (xPriority != yPriority)
            {
                if (xPriority == filesLast.Count)
                    return -1;
                if (yPriority == filesLast.Count)
                    return 1;
                return xPriority.CompareTo(yPriority);
            }

            // Sort by operation type, i.e., 'table', 'function', 'view'
            xPriority = GetSubstringPriority(typeOrder, x);
            yPriority = GetSubstringPriority(typeOrder, y);
            if (xPriority != yPriority)
                return xPriority.CompareTo(yPriority);

            // Sort specific files higher
            xPriority = GetSubstringPriority(filesFirstWithinType, x);
            yPriority = GetSubstringPriority(filesFirstWithinType, y);
            if (xPriority != yPriority)
                return xPriority.CompareTo(yPriority);

            // Sort alphabetically
            return string.Compare(x, y, StringComparison.Ordinal);
        }

        private int GetSubstringPriority(List<string> substringPriority, string str)
        {
            for (int i = 0; i < substringPriority.Count; i++)
            {
                if (str.Contains(substringPriority[i], StringComparison.OrdinalIgnoreCase))
                    return i;
            }

            return substringPriority.Count;
        }
    }
    public string[] SortResourcesNames(string[] resourceNames, CSG.Adapter.Configuration.Options options)
    {
        Utils UCAUtils = new Utils();
        string SharedDatabase = UCAUtils.ReadSetting("CSG_SQLDATABASESHAREDDATABASE").ToString();
        List<string> resourceList = new List<string>(resourceNames);
        if (SharedDatabase.ToLower() == "true")
        {
            resourceList.Remove($"Schema.{options.Database.Type}.functions.cron_jobs.sql");
            resourceList.Remove($"Schema.{options.Database.Type}.functions.cron_jobs_shareddatabase.sql");
        }
        else
        {
            resourceList.Remove($"Schema.{options.Database.Type}.functions.cron_jobs_shareddatabase.sql");
        }
        return resourceList.ToArray();
    }

    public void InstallSystem(CSG.Adapter.Configuration.Options options)
    {
        // Wait for all other processes to terminate. When the Install job is
        // running, all other processes will see this and initiate shutdown and
        // refuse to run.
        var timer = System.Diagnostics.Stopwatch.StartNew();
        var jobs = Enum.GetNames(typeof(CSG.Adapter.Configuration.Job));

        while (true)
        {
            bool allProcessesTerminated = true;
            foreach (var job in jobs)
            {
                if (job == nameof(CSG.Adapter.Configuration.Job.Install))
                    continue;
                // TODO: Inter-container locking
                //if (IsLockTaken("GenesysCloudDataAdapter", job))
                //{
                //    allProcessesTerminated = false;
                //    _logger.LogInformation($"Waiting for other processes to shut down. Job '{job}' is still running.");
                //    Thread.Sleep(TimeSpan.FromSeconds(10));
                //    break;
                //}
            }
            if (allProcessesTerminated)
            {
                if (timer.Elapsed > TimeSpan.FromSeconds(5))
                    _logger.LogInformation("All other processes terminated, install is proceeding.");
                break;
            }
            if (timer.Elapsed > TimeSpan.FromMinutes(5))
                throw new TimeoutException("Timed out waiting for other processes to terminate.");
        }
        Thread.Sleep(TimeSpan.FromSeconds(3));

        if (options.Database == null) throw new ArgumentNullException(nameof(options.Database));

        // Cache clearing has been removed
        _logger.LogInformation("Starting installation process");

        // Create a DBUtils instance
        DBUtils.DBUtils DB = new(_logger)
        {
            DBType = options.Database.Type ?? CSG.Adapter.Configuration.DatabaseType.PostgreSQL,
            PostgresSchema = options.Database.Schema
        };
        DB.DBConnectionString = DB.BuildConnectionString(options.Database);
        DB.ConnectToDatabase();

        // Display current database and schema to assist with troubleshooting.
        if (options.Database.Type == CSG.Adapter.Configuration.DatabaseType.PostgreSQL)
        {
            DataTable res = DB.GetSQLTableData(
                "SELECT setting, current_schema(), current_database() FROM pg_settings WHERE name = 'search_path'",
                "SearchPath");
            if (res != null && res.Rows.Count > 0)
                _logger.LogDebug(
                    "Postgres search path is {0}, schema is {1}, database is {2}",
                    res.Rows[0]["setting"],
                    res.Rows[0]["current_schema"],
                    res.Rows[0]["current_database"]);
        }

        // Get the SQL files that were complied into the assembly.
        Assembly assembly = Assembly.GetExecutingAssembly();
        string[] resourceNames = assembly.GetManifestResourceNames();
        Array.Sort(resourceNames, new SchemaFileComparer());
        resourceNames = SortResourcesNames(resourceNames, options);
        var exceptions = new List<Exception>();
        var totalResources = 0;
        foreach (string resourceName in resourceNames)
        {
            if (!resourceName.EndsWith(".sql"))
                continue;
            if (!resourceName.StartsWith($"Schema.{options.Database.Type}."))
                continue;
            totalResources++;

            string tableName = "";
            string[] splits = resourceName.Split(new string[] { "tables." }, StringSplitOptions.RemoveEmptyEntries);
            if (splits.Length >= 2)
            {
                tableName = splits[1].Split('.')[0];
            }


            using Stream? resourceStream = assembly.GetManifestResourceStream(resourceName)
                ?? throw new ArgumentNullException(resourceName, "Resource has no content");
            using StreamReader resourceReader = new(resourceStream);
            string sqlStatement = resourceReader.ReadToEnd();

            // Split the SQL statement into sections, split by "GO" on the line by itself.
            string[] sqlSections = Regex.Split(sqlStatement, @"(?<=\n|^)GO(?=\n|$)", RegexOptions.Multiline)
                      .Where(section => !string.IsNullOrWhiteSpace(section.Trim()))
                      .ToArray();

            for (int section = 1; section <= sqlSections.Length; section++)
            {
                try
                {
                    int result = 0;
                    if(resourceName.Contains("cron_jobs_shareddatabase"))
                    {
                        // Create a deep copy of the database options for the shared database
                        CSG.Adapter.Configuration.Database sharedDatabaseOptions = new CSG.Adapter.Configuration.Database
                        {
                            Type = options.Database.Type,
                            Address = options.Database.Address,
                            Port = options.Database.Port,
                            Name = "postgres", // Use postgres database for cron schema
                            Schema = "cron",   // Use cron schema
                            User = options.Database.User,
                            Password = options.Database.Password,
                            ConnectOptions = options.Database.ConnectOptions
                        };

                        DBUtils.DBUtils sharedDatabaseDB = new(_logger)
                        {
                            DBType = options.Database.Type ?? CSG.Adapter.Configuration.DatabaseType.PostgreSQL,
                            PostgresSchema = "cron"
                        };

                        // Build connection string with the shared database options
                        sharedDatabaseDB.DBConnectionString = sharedDatabaseDB.BuildConnectionString(sharedDatabaseOptions);
                        _logger.LogDebug("Connecting to shared database with schema 'cron' for cron jobs installation");
                        sharedDatabaseDB.ConnectToDatabase();

                        // Use specialized installation method to ensure caching is bypassed
                        result = sharedDatabaseDB.ExecuteSqlNonQueryForInstall(sqlSections[section-1], 900);
                    }
                    else
                    {
                        // Use specialized installation method to ensure caching is bypassed
                        result = DB.ExecuteSqlNonQueryForInstall(sqlSections[section-1], 900);
                        if (!string.IsNullOrEmpty(tableName) && !tableName.Contains("tabledefinitions"))
                        {
                            IncrementTableVersion(options, tableName);
                        }
                    }
                    if (result >= 0)
                    {
                        if (sqlSections.Length > 1)
                            _logger.LogInformation(
                                "Installed {Resource} (section {Section}/{Sections}), {Changes} row(s) affected",
                                resourceName,
                                section,
                                sqlSections.Length,
                                result);
                        else
                            _logger.LogInformation(
                                "Installed {Resource}, {Changes} row(s) affected",
                                resourceName,
                                result);
                    }
                    else
                    {
                        if (sqlSections.Length > 1)
                            _logger.LogInformation("Installed {Resource} (section {Section}/{Sections})",
                                resourceName,
                                section,
                                sqlSections.Length);
                        else
                            _logger.LogInformation("Installed {Resource}", resourceName);
                    }
                }
                catch (Exception ex)
                {
                    var message = Regex.Replace(ex.Message, @"\s+", " ");
                    if (sqlSections.Length > 1)
                        _logger.LogError(ex, "Failed to install {Resource} (section {Section}/{Sections}), {Message}",
                            resourceName,
                            section,
                            sqlSections.Length,
                            message);
                    else
                        _logger.LogError(ex, "Failed to install {Resource}, {Message}",
                            resourceName,
                            message);
                    exceptions.Add(new SchemaException($"{message} in {resourceName}", ex));
                }
            }
        }
        if (exceptions.Any())
        {
            _logger.LogWarning(
                "Failed to install {FailedResources} of {TotalResources} resources",
                exceptions.Count,
                totalResources
            );


            throw new AggregateException("Failed to install database schema", exceptions);
        }



        _logger.LogInformation("Installed {TotalResources} resources", totalResources);
    }

    private void IncrementTableVersion(CSG.Adapter.Configuration.Options options, string tableName)
    {
        DBUtils.DBUtils DB = new(_logger)
        {
            DBType = options.Database.Type ?? CSG.Adapter.Configuration.DatabaseType.PostgreSQL,
            PostgresSchema = options.Database.Schema
        };
        DB.DBConnectionString = DB.BuildConnectionString(options.Database);
        DB.ConnectToDatabase();

        string detailedVersionNumber = ApplicationVersion.MajorMinorPatch.ToString();

        string updateQuery = $"UPDATE tabledefinitions SET Version = '"+detailedVersionNumber +"' WHERE TableName = '" + tableName +"'";
        // Use specialized installation method to ensure caching is bypassed
        DB.ExecuteSqlNonQueryForInstall(updateQuery);
    }

}
