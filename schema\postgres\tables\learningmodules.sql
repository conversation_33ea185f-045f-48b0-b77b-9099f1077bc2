DROP VIEW IF EXISTS vwLearningAssignmentCorrelation CASCADE;
DROP VIEW IF EXISTS vwLearningModuleCompletionAnalytics CASCADE;
DROP VIEW IF EXISTS vwLearningUserAssignmentSummary CASCADE;

CREATE TABLE IF NOT EXISTS learningmodules (
    id VARCHAR(50) NOT NULL,
    name VARCHA<PERSON>(100),
    description VARCHAR(500),
    version VARCHAR(255),
    externalId VARCHAR(50),
    source VARCHAR(50),
    enforceContentOrder BOOLEAN,
    isArchived BOOLEAN,
    isPublished BOOLEAN,
    completionTimeInDays INTEGER,
    type VARCHAR(50),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT learningmodule_pkey PRIMARY KEY (id)
);

ALTER TABLE learningmodules
DROP COLUMN IF EXISTS state;

-- Update description column length for existing installations
DO $$
BEGIN
    -- Check if description column exists and needs to be expanded
    IF EXISTS (
        SELECT 1 FROM information_schema.columns
        WHERE table_name = 'learningmodules'
        AND column_name = 'description'
        AND character_maximum_length < 500
    ) THEN
        ALTER TABLE learningmodules ALTER COLUMN description TYPE VARCHAR(500);
    END IF;
END $$;