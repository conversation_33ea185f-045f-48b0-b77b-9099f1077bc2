﻿# Voice Analysis Tests

This project contains unit tests for the VoiceAnalysis class in the GenesysCloudUtils project. The tests focus on the queue verification and transcript ingestion functionality.

## Test Structure

The tests are organized into two main classes:

1. `VoiceAnalysisTests` - Basic tests for the VoiceAnalysis class
2. `VoiceAnalysisMockTests` - Tests that use mocked HTTP responses to test the API interactions

## Running the Tests

To run the tests, use the following command from the project directory:

```bash
dotnet test
```

For more detailed output, use:

```bash
dotnet test --logger "console;verbosity=detailed"
```

## Test Coverage

The tests cover the following functionality:

- Constructor and initialization
- Queue verification with retry logic
- Transcript ingestion with rate limiting handling
- Transcript URL retrieval
- Transcript downloading

## Skipped Tests

Some tests are marked as `[Skip]` because they require external dependencies or HTTP mocking that would be complex to set up. These tests serve as documentation for what should be tested in a more comprehensive test environment.

## Future Improvements

The VoiceAnalysis class could be refactored to improve testability by:

1. Using dependency injection for HTTP clients
2. Using interfaces for external dependencies
3. Replacing WebClient with <PERSON>ttpClient throughout the codebase
4. Adding more comprehensive mocking of external services

## Notes on HTTP Mocking

The tests demonstrate how to mock HTTP responses using Moq and HttpClient. This approach allows testing of HTTP-dependent code without making actual network calls.

Example of HTTP mocking:

```csharp
var mockHandler = new Mock<HttpMessageHandler>();
mockHandler
    .Protected()
    .Setup<Task<HttpResponseMessage>>(
        "SendAsync",
        ItExpr.IsAny<HttpRequestMessage>(),
        ItExpr.IsAny<CancellationToken>()
    )
    .ReturnsAsync(new HttpResponseMessage
    {
        StatusCode = HttpStatusCode.OK,
        Content = new StringContent(content, Encoding.UTF8, "application/json")
    });

var httpClient = new HttpClient(mockHandler.Object);
```
