﻿-- User Assignment Summary View
-- Provides comprehensive user-centric view of learning module assignments and completion status
-- Combines assignment data with results to show complete learning lifecycle per user
--
-- PURPOSE:
-- This view serves as the primary interface for user-centric learning analytics, combining
-- assignment data from learningmoduleassignments with completion results from learningassignmentresults.
-- It provides a complete picture of each user's learning journey, including assignment status,
-- completion metrics, and performance indicators.
--
-- USE CASES:
-- - User learning dashboards and progress tracking
-- - Manager oversight of team learning completion
-- - HR reporting on training compliance and completion rates
-- - Individual user learning history and transcript generation
-- - Learning analytics and performance trend analysis
--
-- KEY FEATURES:
-- - Complete user demographics (name, email, department)
-- - Assignment details (dates, status, overdue flags)
-- - Module information (name, description)
-- - Completion metrics (scores, percentages, duration)
-- - Calculated status fields (completion_status, pass_status)
-- - Performance indicators and timing analysis
--
-- PERFORMANCE OPTIMIZATIONS:
-- - Leverages userid+moduleid relationships for efficient joins
-- - Uses module-based API retrieval strategy for optimal data collection
-- - Includes proper indexing hints through foreign key relationships

CREATE OR REPLACE VIEW vwLearningUserAssignmentSummary AS
SELECT
    u.id AS user_id,
    u.name AS user_name,
    u.email AS user_email,
    u.department AS user_department,
    lma.id AS assignment_id,
    lma.moduleid AS module_id,
    lm.name AS module_name,
    lm.description AS module_description,
    lma.state AS assignment_state,
    lma.isOverdue AS is_overdue,
    lma.dateAssigned AS date_assigned,
    lma.dateDue AS date_due,
    lma.dateRecommendedForCompletion AS date_recommended_completion,
    lar.id AS result_id,
    lar.assessmentPercentageScore AS assessment_score,
    lar.completionPercentage AS completion_percentage,
    lar.passPercent AS pass_percentage,
    lar.lengthInMinutes AS duration_minutes,
    lar.dateCreated AS result_date_created,
    lar.dateModified AS result_date_modified,

    -- Calculated completion status based on assignment and result data
    -- Priority: Completed > Overdue > Assigned > Unknown
    CASE
        WHEN lar.id IS NOT NULL THEN 'Completed'  -- Has completion record
        WHEN lma.isOverdue = true THEN 'Overdue'  -- Past due date
        WHEN lma.state = 'Assigned' THEN 'Assigned'  -- Currently assigned
        ELSE 'Unknown'  -- Fallback for undefined states
    END AS completion_status,

    -- Pass/fail determination based on assessment scores vs. required pass percentage
    -- Only calculated when both assessment score and pass threshold are available
    CASE
        WHEN lar.assessmentPercentageScore IS NOT NULL AND lar.passPercent IS NOT NULL
        THEN CASE WHEN lar.assessmentPercentageScore >= lar.passPercent THEN 'Passed' ELSE 'Failed' END
        ELSE NULL  -- No assessment data available
    END AS pass_status
FROM userdetails u
    -- LEFT JOIN to include all users, even those without assignments
    LEFT JOIN learningmoduleassignments lma ON u.id = lma.userid
    -- LEFT JOIN to get module details for assigned modules
    LEFT JOIN learningmodules lm ON lma.moduleid = lm.id
    -- LEFT JOIN to get completion results using userid+moduleid composite relationship
    -- This join enables complete assignment-to-completion lifecycle tracking
    LEFT JOIN learningassignmentresults lar ON lma.userid = lar.userid AND lma.moduleid = lar.moduleid
WHERE u.state != 'deleted'  -- Exclude deleted users from results
ORDER BY u.name, lm.name;  -- Sort by user name, then module name for consistent output

-- Add comment explaining the view purpose and relationships
COMMENT ON VIEW vwLearningUserAssignmentSummary IS
'Comprehensive user-centric view combining learning assignments and results.
Shows complete learning lifecycle per user including assignment status, completion status, and performance metrics.
Key relationships: userdetails -> learningmoduleassignments -> learningassignmentresults via userid+moduleid.
Performance optimized through module-based API retrieval strategy.';
