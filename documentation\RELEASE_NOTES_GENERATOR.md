# Genesys Adapter Release Notes Generator

This system automatically generates HTML release notes by analyzing actual code changes between git tags, rather than relying on commit messages.

## Quick Start

### For a New Monthly Section
```powershell
.\create_release_notes.ps1
# When prompted, answer 'y' for new monthly section
```

### For Adding to Existing Month
```powershell
.\create_release_notes.ps1 -ExistingMonth
```

### Manual Tag Selection
```powershell
.\create_release_notes.ps1 -FromTag "v3.48.5" -ToTag "v3.48.6.0"
```

## How It Works

1. **Tag Detection**: Automatically finds the last two git tags
2. **Code Analysis**: Examines actual file changes between tags
3. **Pattern Recognition**: Identifies specific types of improvements:
   - Data consistency fixes (sorting, duplicates, isolation)
   - Technical enhancements (async processing, null safety)
   - Database updates (schema changes, sync improvements)
   - Component tracking (affected modules)
4. **HTML Generation**: Creates properly formatted HTML for the support portal

## Output Formats

### New Monthly Section
Creates a complete monthly section with header:
```html
<div class="release-month">
    <h2>July 2025</h2>
    <div class="release-version">
        <h3>Version v3.48.6.0 - July 10, 2025</h3>
        <!-- Release content -->
    </div>
</div>
```

### Add to Existing Month
Creates just the version section to add to existing month:
```html
<div class="release-version">
    <h3>Version v3.48.6.0 - July 10, 2025</h3>
    <!-- Release content -->
</div>
```

## Generated Categories

The system automatically categorizes changes into:

- **Data Consistency Improvements**: Fixes for duplicates, sorting, isolation
- **Technical Enhancements**: Async processing, error handling, validation
- **Database Updates**: Schema changes, sync improvements
- **Affected Components**: Lists the modules/components changed

## Files Generated

- `release_notes_[Month]_[Year]_new.html` - For new monthly sections
- `release_notes_[version]_add.html` - For adding to existing months

## Code Analysis Patterns

The system recognizes these code change patterns:

### Data Consistency
- `OrderBy|Sort.*participants|Sort.*sessions|Sort.*segments` → Sorting fixes
- `isolation|ProcessContactListsWithIsolationAsync` → Isolation improvements
- `FirstQueueId|LastQueueId.*null.*check` → Queue ID fixes

### Technical Enhancements
- `null.*safety|null.*check` → Null safety improvements
- `async.*Task|ProcessContactListsWithIsolationAsync` → Async enhancements
- `keyid.*format.*validation` → Validation improvements

### Database Updates
- SQL files with `datekeyfield` changes → Sync date updates

### Component Tracking
- `OutboundDialingData` → Outbound Dialing processing
- `SegmentData` → Conversation Segment processing
- `schema.*functions` → Database functions

## Usage Tips

1. **Always review the generated HTML** before adding to the support portal
2. **Confirm the tag selection** when prompted - the system shows the last two tags
3. **Use descriptive summaries** - the system creates human-readable descriptions from code patterns
4. **Check affected components** - ensures all impacted areas are documented

## Example Output

For v3.48.6.0, the system generated:

**Data Consistency Improvements:**
- Enhanced contact list processing with isolated processing to prevent data cross-contamination
- Fixed duplicate conversation segments by implementing consistent sorting of participants, sessions, and segments
- Improved queue ID assignment logic with proper null handling

**Technical Enhancements:**
- Enhanced asynchronous processing for contact list operations
- Added robust null safety checks throughout conversation data processing

**Database Updates:**
- Simplified database sync scripts for conversation summary data across all database types

**Affected Components:**
- Outbound Dialing Data processing
- Conversation Segment Data processing
- Database synchronization functions

## Integration with Support Portal

The generated HTML is designed to be directly copied into the Customer Science support portal release notes page at:
https://support2.customerscience.com.au/portal/en/kb/articles/release-notes

Simply copy the generated HTML and paste it into the appropriate monthly section.
