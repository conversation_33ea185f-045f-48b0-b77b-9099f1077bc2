IF dbo.csg_table_exists('queueInteractionData') = 0
CREATE TABLE [queueInteractionData](
    [keyid] [nvarchar](255) NOT NULL,
    [direction] [nvarchar](50),
    [queueid] [nvarchar](50),
    [mediatype] [nvarchar](50),
    [wrapupcode] [nvarchar](255),
    [startdate] [datetime],
    [startdateltc] [datetime],
    [talertcount] [int],
    [talerttimesum] [decimal](20, 2),
    [talerttimemax] [decimal](20, 2),
    [talerttimemin] [decimal](20, 2),
    [tansweredcount] [int],
    [tansweredtimesum] [decimal](20, 2),
    [tansweredtimemax] [decimal](20, 2),
    [tansweredtimemin] [decimal](20, 2),
    [ttalkcount] [int],
    [ttalktimesum] [decimal](20, 2),
    [ttalktimemax] [decimal](20, 2),
    [ttalktimemin] [decimal](20, 2),
    [ttalkcompletecount] [int],
    [ttalkcompletetimesum] [decimal](20, 2),
    [ttalkcompletetimemax] [decimal](20, 2),
    [ttalkcompletetimemin] [decimal](20, 2),
    [tnotrespondingcount] [int],
    [tnotrespondingtimesum] [decimal](20, 2),
    [tnotrespondingtimemax] [decimal](20, 2),
    [tnotrespondingtimemin] [decimal](20, 2),
    [theldcount] [int],
    [theldtimesum] [decimal](20, 2),
    [theldtimemax] [decimal](20, 2),
    [theldtimemin] [decimal](20, 2),
    [theldcompletecount] [int],
    [theldcompletetimesum] [decimal](20, 2),
    [theldcompletetimemax] [decimal](20, 2),
    [theldcompletetimemin] [decimal](20, 2),
    [thandlecount] [int],
    [thandletimesum] [decimal](20, 2),
    [thandletimemax] [decimal](20, 2),
    [thandletimemin] [decimal](20, 2),
    [tacwcount] [int],
    [tacwtimesum] [decimal](20, 2),
    [tacwtimemax] [decimal](20, 2),
    [tacwtimemin] [decimal](20, 2),
    [nconsult] [int],
    [nconsulttransferred] [int],
    [noutbound] [int],
    [nerror] [int],
    [ntransferred] [int],
    [nblindtransferred] [int],
    [nconnected] [int],
    [noffered] [int],
    [noversla] [int],
    [tacdcount] [int],
    [tacdtimesum] [decimal](20, 2),
    [tacdtimemax] [decimal](20, 2),
    [tacdtimemin] [decimal](20, 2),
    [tdialingcount] [int],
    [tdialingtimesum] [decimal](20, 2),
    [tdialingtimemax] [decimal](20, 2),
    [tdialingtimemin] [decimal](20, 2),
    [tcontactingcount] [int],
    [tcontactingtimesum] [decimal](20, 2),
    [tcontactingtimemax] [decimal](20, 2),
    [tcontactingtimemin] [decimal](20, 2),
    [tvoicemailcount] [int],
    [tvoicemailtimesum] [decimal](20, 2),
    [tvoicemailtimemax] [decimal](20, 2),
    [tvoicemailtimemin] [decimal](20, 2),
    [tflowoutcount] [int],
    [tflowouttimesum] [decimal](20, 2),
    [tflowouttimemax] [decimal](20, 2),
    [tflowouttimemin] [decimal](20, 2),
    [twaitcount] [int],
    [twaittimesum] [decimal](20, 2),
    [twaittimemax] [decimal](20, 2),
    [twaittimemin] [decimal](20, 2),
    [tabandoncount] [int],
    [tabandontimesum] [decimal](20, 2),
    [tabandontimemax] [decimal](20, 2),
    [tabandontimemin] [decimal](20, 2),
    [servicelevelnumerator] [decimal](20, 2),
    [serviceleveldenominator] [decimal](20, 2),
    [av1count] [int],
    [av1timesum] [decimal](20, 2),
    [av1timemax] [decimal](20, 2),
    [av1timemin] [decimal](20, 2),
    [av2count] [int],
    [av2timesum] [decimal](20, 2),
    [av2timemax] [decimal](20, 2),
    [av2timemin] [decimal](20, 2),
    [av3count] [int],
    [av3timesum] [decimal](20, 2),
    [av3timemax] [decimal](20, 2),
    [av3timemin] [decimal](20, 2),
    [av4count] [int],
    [av4timesum] [decimal](20, 2),
    [av4timemax] [decimal](20, 2),
    [av4timemin] [decimal](20, 2),
    [av5count] [int],
    [av5timesum] [decimal](20, 2),
    [av5timemax] [decimal](20, 2),
    [av5timemin] [decimal](20, 2),
    [av6count] [int],
    [av6timesum] [decimal](20, 2),
    [av6timemax] [decimal](20, 2),
    [av6timemin] [decimal](20, 2),
    [av7count] [int],
    [av7timesum] [decimal](20, 2),
    [av7timemax] [decimal](20, 2),
    [av7timemin] [decimal](20, 2),
    [av8count] [int],
    [av8timesum] [decimal](20, 2),
    [av8timemax] [decimal](20, 2),
    [av8timemin] [decimal](20, 2),
    [av9count] [int],
    [av9timesum] [decimal](20, 2),
    [av9timemax] [decimal](20, 2),
    [av9timemin] [decimal](20, 2),
    [av10count] [int],
    [av10timesum] [decimal](20, 2),
    [av10timemax] [decimal](20, 2),
    [av10timemin] [decimal](20, 2),
    [oservicetarget] [int],
    [tivrcount] [int],
    [tivrtimesum] [decimal](20, 2),
    [tivrtimemax] [decimal](20, 2),
    [tivrtimemin] [decimal](20, 2),
    [tshortabandoncount] [int],
    [tshortabandontimesum] [decimal](20, 2),
    [tshortabandontimemax] [decimal](20, 2),
    [tshortabandontimemin] [decimal](20, 2),
    [tuserresponsetimecount] [int],
    [tuserresponsetimetimesum] [decimal](20, 2),
    [tuserresponsetimetimemax] [decimal](20, 2),
    [tuserresponsetimetimemin] [decimal](20, 2),
    [tagentresponsetimecount] [int],
    [tagentresponsetimetimesum] [decimal](20, 2),
    [tagentresponsetimetimemax] [decimal](20, 2),
    [tagentresponsetimetimemin] [decimal](20, 2),
    [updated] [datetime],
    CONSTRAINT [PK_queueInteractionData] PRIMARY KEY ([keyid])
);

IF dbo.csg_index_exists('queueInteractionData_Date', 'queueInteractionData') = 0
CREATE INDEX [queueInteractionData_Date] ON [queueInteractionData] ([startdate]);
IF dbo.csg_index_exists('queueInteractionData_DateLTC', 'queueInteractionData') = 0
CREATE INDEX [queueInteractionData_DateLTC] ON [queueInteractionData] ([startdateltc]);
IF dbo.csg_index_exists('queueInteractionData_Media', 'queueInteractionData') = 0
CREATE INDEX [queueInteractionData_Media] ON [queueInteractionData] ([mediatype]);

-- Add missing queue-specific interaction metrics columns for comprehensive Genesys Cloud data support
IF dbo.csg_column_exists('queueInteractionData', 'oservicetarget') = 0
    ALTER TABLE queueInteractionData ADD oservicetarget INT;
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN oservicetarget INT;

IF dbo.csg_column_exists('queueInteractionData', 'tivrcount') = 0
    ALTER TABLE queueInteractionData ADD tivrcount INT;
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tivrcount INT;

IF dbo.csg_column_exists('queueInteractionData', 'tivrtimesum') = 0
    ALTER TABLE queueInteractionData ADD tivrtimesum DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tivrtimesum DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tivrtimemax') = 0
    ALTER TABLE queueInteractionData ADD tivrtimemax DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tivrtimemax DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tivrtimemin') = 0
    ALTER TABLE queueInteractionData ADD tivrtimemin DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tivrtimemin DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tshortabandoncount') = 0
    ALTER TABLE queueInteractionData ADD tshortabandoncount INT;
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tshortabandoncount INT;

IF dbo.csg_column_exists('queueInteractionData', 'tshortabandontimesum') = 0
    ALTER TABLE queueInteractionData ADD tshortabandontimesum DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tshortabandontimesum DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tshortabandontimemax') = 0
    ALTER TABLE queueInteractionData ADD tshortabandontimemax DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tshortabandontimemax DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tshortabandontimemin') = 0
    ALTER TABLE queueInteractionData ADD tshortabandontimemin DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tshortabandontimemin DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tuserresponsetimecount') = 0
    ALTER TABLE queueInteractionData ADD tuserresponsetimecount INT;
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tuserresponsetimecount INT;

IF dbo.csg_column_exists('queueInteractionData', 'tuserresponsetimetimesum') = 0
    ALTER TABLE queueInteractionData ADD tuserresponsetimetimesum DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tuserresponsetimetimesum DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tuserresponsetimetimemax') = 0
    ALTER TABLE queueInteractionData ADD tuserresponsetimetimemax DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tuserresponsetimetimemax DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tuserresponsetimetimemin') = 0
    ALTER TABLE queueInteractionData ADD tuserresponsetimetimemin DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tuserresponsetimetimemin DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tagentresponsetimecount') = 0
    ALTER TABLE queueInteractionData ADD tagentresponsetimecount INT;
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tagentresponsetimecount INT;

IF dbo.csg_column_exists('queueInteractionData', 'tagentresponsetimetimesum') = 0
    ALTER TABLE queueInteractionData ADD tagentresponsetimetimesum DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tagentresponsetimetimesum DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tagentresponsetimetimemax') = 0
    ALTER TABLE queueInteractionData ADD tagentresponsetimetimemax DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tagentresponsetimetimemax DECIMAL(20, 2);

IF dbo.csg_column_exists('queueInteractionData', 'tagentresponsetimetimemin') = 0
    ALTER TABLE queueInteractionData ADD tagentresponsetimetimemin DECIMAL(20, 2);
ELSE
    ALTER TABLE queueInteractionData ALTER COLUMN tagentresponsetimetimemin DECIMAL(20, 2);
IF dbo.csg_index_exists('queueInteractionData_Queue', 'queueInteractionData') = 0
CREATE INDEX [queueInteractionData_Queue] ON [queueInteractionData] ([queueid]);
