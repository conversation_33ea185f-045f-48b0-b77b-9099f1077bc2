IF dbo.csg_table_exists('learningmodules') = 0
CREATE TABLE [learningmodules] (
    [id] VARCHAR(50) NOT NULL,
    [name] VARCHAR(100),
    [description] VARCHAR(500),
    [version] VARCHAR(255),
    [externalId] VARCHAR(50),
    [source] VARCHAR(50),
    [enforceContentOrder] BIT,
    [isArchived] BIT,
    [isPublished] BIT,
    [completionTimeInDays] INT,
    [type] VARCHAR(50),
    [dateCreated] DATETIME,
    [dateModified] DATETIME,
    [lengthInMinutes] DECIMAL(20, 2),
    [state] VARCHAR(50), -- Current state of the learning module (e.g., active, deleted, archived)
    [updated] DATETIME,
    CONSTRAINT [learningmodule_pkey] PRIMARY KEY ([id])
);

IF dbo.csg_column_exists('learningmodules', 'state') = 1
    ALTER TABLE learningmodules DROP COLUMN state;

-- Update description column length for existing installations
IF dbo.csg_table_exists('learningmodules') = 1 AND dbo.csg_column_exists('learningmodules', 'description') = 1
BEGIN
    -- Check if description column needs to be expanded
    DECLARE @current_length INT;
    SELECT @current_length = CHARACTER_MAXIMUM_LENGTH
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_NAME = 'learningmodules' AND COLUMN_NAME = 'description';

    IF @current_length < 500
        ALTER TABLE learningmodules ALTER COLUMN [description] VARCHAR(500);
END
