﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using GenesysCloudUtils;
using Microsoft.Extensions.Logging;
using Moq;

namespace VoiceAnalysisTestProject
{
    internal static class QueueVerificationCacheTest
    {
        public static async Task RunTest()
        {
            Console.WriteLine("\nRunning Queue Verification Cache Test...");

            // Create a mock logger
            var loggerMock = new Mock<ILogger<VoiceAnalysis>>();

            // Create a VoiceAnalysis instance
            var voiceAnalysis = new VoiceAnalysis(loggerMock.Object);

            // Create a cache for queue verification results
            Dictionary<string, bool> queueVerificationCache = new Dictionary<string, bool>();

            // Create a DataTable to simulate the overview table
            DataTable overviewTable = new DataTable();
            overviewTable.Columns.Add("conversationid", typeof(string));
            overviewTable.Columns.Add("queueid", typeof(string));

            // Add rows with the same queue ID to simulate multiple conversations using the same queue
            string queueId = "test-queue-id";
            for (int i = 1; i <= 5; i++)
            {
                DataRow row = overviewTable.NewRow();
                row["conversationid"] = $"conversation-{i}";
                row["queueid"] = queueId;
                overviewTable.Rows.Add(row);
            }

            // Process each conversation
            Console.WriteLine("Processing conversations with the same queue ID...");
            int apiCallCount = 0;

            foreach (DataRow row in overviewTable.Rows)
            {
                string conversationId = row["conversationid"].ToString();
                string currentQueueId = row["queueid"].ToString();

                // Check if we've already verified this queue
                if (queueVerificationCache.TryGetValue(currentQueueId, out bool cachedResult))
                {
                    Console.WriteLine($"Conversation {conversationId}: Using cached result for queue {currentQueueId}: {cachedResult}");
                }
                else
                {
                    // If not in cache, perform the verification
                    bool queueVerified = await voiceAnalysis.VerifyQueueAsync(currentQueueId);
                    apiCallCount++;

                    // Cache the result for future use
                    queueVerificationCache[currentQueueId] = queueVerified;

                    Console.WriteLine($"Conversation {conversationId}: Verified queue {currentQueueId}: {queueVerified}");
                }
            }

            Console.WriteLine($"Total API calls: {apiCallCount}");
            Console.WriteLine($"Total conversations: {overviewTable.Rows.Count}");
            Console.WriteLine($"API calls saved: {overviewTable.Rows.Count - apiCallCount}");

            // Now test with different queue IDs
            Console.WriteLine("\nProcessing conversations with different queue IDs...");

            // Clear the table and cache
            overviewTable.Clear();
            queueVerificationCache.Clear();
            apiCallCount = 0;

            // Add rows with different queue IDs
            for (int i = 1; i <= 5; i++)
            {
                DataRow row = overviewTable.NewRow();
                row["conversationid"] = $"conversation-{i}";
                row["queueid"] = $"queue-{i}";
                overviewTable.Rows.Add(row);
            }

            // Process each conversation
            foreach (DataRow row in overviewTable.Rows)
            {
                string conversationId = row["conversationid"].ToString();
                string currentQueueId = row["queueid"].ToString();

                // Check if we've already verified this queue
                if (queueVerificationCache.TryGetValue(currentQueueId, out bool cachedResult))
                {
                    Console.WriteLine($"Conversation {conversationId}: Using cached result for queue {currentQueueId}: {cachedResult}");
                }
                else
                {
                    // If not in cache, perform the verification
                    bool queueVerified = await voiceAnalysis.VerifyQueueAsync(currentQueueId);
                    apiCallCount++;

                    // Cache the result for future use
                    queueVerificationCache[currentQueueId] = queueVerified;

                    Console.WriteLine($"Conversation {conversationId}: Verified queue {currentQueueId}: {queueVerified}");
                }
            }

            Console.WriteLine($"Total API calls: {apiCallCount}");
            Console.WriteLine($"Total conversations: {overviewTable.Rows.Count}");
            Console.WriteLine($"API calls saved: {overviewTable.Rows.Count - apiCallCount}");
        }
    }
}
