-- PostgreSQL: Update convsummarydata datekeyfield in tabledefinitions
-- Only runs for versions < 3.48.6 but within 3.48.% range
-- This script is automatically executed during installation/upgrade

-- Update convsummarydata datekeyfield for versions < 3.48.6 but is 3.48.%
UPDATE tabledefinitions
SET datekeyfield = '2025-06-23 00:00:00'::timestamp
WHERE tablename IN ('convsummarydata','detailedinteractiondata')
AND version LIKE '3.48.%'
AND version < '********';

GO

DELETE FROM detailedinteractiondata did
USING tabledefinitions td
WHERE 'detailedinteractiondata' = td.tablename
  AND did.conversationstartdate > '2025-06-23 00:00:00'
  AND td.version LIKE '3.48.%'
  AND td.version < '********';