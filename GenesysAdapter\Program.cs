using System.Globalization;
using System.Text;
using CSG.Common.ExtensionMethods;
using DBUtils;
using GenesysAdapter;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Logging.ApplicationInsights;
using Newtonsoft.Json.Linq;
using Serilog;
using Serilog.Exceptions;
using StandardUtils;

class Program
{
    private static Microsoft.Extensions.Logging.ILogger? _logger;
    private static TelemetryClient? _telemetry;
    private readonly static System.Diagnostics.Stopwatch _appTimer = System.Diagnostics.Stopwatch.StartNew();
    private static string _legacyUsage = "";
    private static TimeZoneInfo _timeZone = TimeZoneInfo.Utc;
    static async Task Main(string[] args)
    {
        const string azureMonitorConnectionString =
            "InstrumentationKey=f44d80a7-9d40-4515-9d13-b221f88ab65c;" +
            "IngestionEndpoint=https://australiasoutheast-0.in.applicationinsights.azure.com/;" +
            "LiveEndpoint=https://australiasoutheast.livediagnostics.monitor.azure.com/";

        AppDomain.CurrentDomain.UnhandledException += new UnhandledExceptionEventHandler(UnhandledExceptionHandler);
        AppDomain.CurrentDomain.ProcessExit += (sender, eventArgs) => OnProcessExit();

        Environment.ExitCode = 0;
        Console.OutputEncoding = Encoding.UTF8;
        CultureInfo culture = CultureInfo.CreateSpecificCulture("en-US");
        Thread.CurrentThread.CurrentCulture = culture;

        var options = new CSG.Adapter.Configuration.Options();
// #if OPTIONS_V3_COMPAT
//         var legacyOptions = new CSG.Adapter.Configuration.LegacyOptions();
// #endif
        var defaultOptions = new Dictionary<string, string?>(StringComparer.OrdinalIgnoreCase)
        {
            [$"{nameof(options.Database)}:{nameof(options.Database.Name)}"]
                = "postgres",
            [$"{nameof(options.Database)}:{nameof(options.Database.Schema)}"]
                = "public",
            [$"{nameof(options.Database)}:{nameof(options.Database.Type)}"]
                = nameof(CSG.Adapter.Configuration.DatabaseType.PostgreSQL),
            [$"{nameof(options.LogLevel)}"] = "Information",
            [$"{nameof(options.Preferences)}:{nameof(options.Preferences.Telemetry)}"] = "true",
            [$"{nameof(options.GenesysApi)}:{nameof(options.GenesysApi.Endpoint)}"]
                = "https://api.mypurecloud.com.au",
            [$"{nameof(options.Preferences)}:{nameof(options.Preferences.OffsetMonths)}"]
                = "1",
            [$"{nameof(options.Preferences)}:{nameof(options.Preferences.TimeZone)}"]
                = "Australia/Sydney",
            [$"{nameof(options.Preferences)}:{nameof(options.Preferences.MaxSyncSpan)}"]
                = "1.00:00:00",
            [$"{nameof(options.Preferences)}:{nameof(options.Preferences.LookBackSpan)}"]
                = "1.00:00:00",
            [$"{nameof(options.Preferences)}:{nameof(options.Preferences.Permissions)}:{nameof(options.Preferences.Permissions.Update)}"]
                = "false",
            [$"{nameof(options.Preferences)}:{nameof(options.Preferences.Permissions)}:{nameof(options.Preferences.Permissions.ForcedUpdate)}"]
                = "false"
        };
        var environmentVariablePrefix = "CSG_";
        IConfigurationRoot configuration = new ConfigurationBuilder()
            .AddInMemoryCollection(defaultOptions)
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile(path: "appsettings.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables(environmentVariablePrefix)
            .AddCommandLine(args)
            .Build();

        var preferences = configuration.GetSection("Preferences").Get<JObject>();

        try
        {
            configuration.Bind(options);
        }
        catch (InvalidOperationException ex) when (ex.Message.StartsWith("Failed to convert"))
        {
            options.Job = CSG.Adapter.Configuration.Job.Information;
            Console.WriteLine("Error parsing configuration options.");
            Console.WriteLine(ex.Message);
            Console.WriteLine(ex.InnerException?.Message);
        }
        if (options.LogLevel == null) options.LogLevel = CSG.Adapter.Configuration.LogLevel.Information;
        if (options.Database == null) options.Database = new CSG.Adapter.Configuration.Database();
        if (options.GenesysApi == null) options.GenesysApi = new CSG.Adapter.Configuration.GenesysApi();
        if (options.Preferences == null) options.Preferences = new CSG.Adapter.Configuration.Preferences();

// #if OPTIONS_V3_COMPAT
//         bool usingCompatOptions = false;
//         bool jobParameterPassed = options.Job != null;
//         try
//         {
//             configuration.Bind(legacyOptions);
//         }
//         catch (InvalidOperationException ex) when (ex.Message.StartsWith("Failed to convert"))
//         {
//             options.Job = CSG.Adapter.Configuration.Job.Information;
//             Console.WriteLine("Error parsing configuration options.");
//             Console.WriteLine(ex.Message);
//             Console.WriteLine(ex.InnerException?.Message);
//         }
//         if (string.IsNullOrEmpty(options.Database.Address)
//             && !string.IsNullOrEmpty(legacyOptions.CustomerKeyId)
//             && legacyOptions.SqlDatabaseType != null
//             && !string.IsNullOrEmpty(legacyOptions.SqlConnectionString))
//         {
//             switch (legacyOptions.SqlDatabaseType)
//             {
//                 case CSG.Adapter.Configuration.LegacyDatabaseType.PostSQL:
//                     options.Database.Type = CSG.Adapter.Configuration.DatabaseType.PostgreSQL;
//                     break;
//                 case CSG.Adapter.Configuration.LegacyDatabaseType.MSSQL:
//                     options.Database.Type = CSG.Adapter.Configuration.DatabaseType.MSSQL;
//                     break;
// #if MYSQL
//                 case CSG.Adapter.Configuration.LegacyDatabaseType.MySQL:
//                     options.Database.Type = CSG.Adapter.Configuration.DatabaseType.MySQL;
//                     break;
// #endif
//                 default:
//                     throw new ArgumentException("Unknown value for legacy option", nameof(legacyOptions.SqlDatabaseType));
//             }
//             Console.WriteLine("Use of CSG_SQLDATABASETYPE environment variable is deprecated, please switch to a configuration file.");
//             usingCompatOptions = true;
//             options.Database.Schema = legacyOptions.SqlDatabaseSchema;
//             options.Database = new DBUtils.DBUtils(_logger).ParseConnectionString(
//                 options.Database,
//                 new Simple3Des(legacyOptions.CustomerKeyId)
//                     .DecryptData(legacyOptions.SqlConnectionString
//                 )
//             );
//         }
//         if (string.IsNullOrEmpty(options.GenesysApi.ClientId)
//             && !string.IsNullOrEmpty(legacyOptions.CustomerKeyId))
//         {
//             Console.WriteLine("Use of CSG_CUSTOMERKEYID environment variable is deprecated, please switch to a configuration file.");
//             usingCompatOptions = true;
//             System.Data.DataRow? gcControlData = new ControlServ.ControlServ()
//                 .CreateGCAdminData(legacyOptions.CustomerKeyId)
//                 .Tables["GCControlData"]?.Rows[0];
//             options.GenesysApi.ClientId = gcControlData?["GC_UserId"] as string;
//             if (!string.IsNullOrEmpty(options.GenesysApi.ClientId)
//                 && !string.IsNullOrEmpty(gcControlData?["GC_Secret"] as string))
//             {
//                 options.GenesysApi.ClientSecret = new Secret(
//                     options.GenesysApi.ClientId,
//                     gcControlData["GC_Secret"] as string ?? ""
//                 ).Encrypted;
//             }
//             if (!string.IsNullOrEmpty(gcControlData?["GC_URL"] as string))
//                 options.GenesysApi.Endpoint = new Uri(gcControlData?["GC_Url"] as string ?? "");
//         }

//         // Parse command line for positional parameters.
//         if (usingCompatOptions && !jobParameterPassed && args.Length > 0 && !string.IsNullOrWhiteSpace(args[0]))
//         {
//             CSG.Adapter.Configuration.Job syncType;
//             if (Enum.TryParse<CSG.Adapter.Configuration.Job>(args[0], true, out syncType))
//             {
//                 options.Job = syncType;
//                 if (args.Length > 1 && !string.IsNullOrWhiteSpace(args[1]))
//                 {
//                     Console.WriteLine("Use of positional parameters is deprecated, please switch to a configuration file.");
//                     usingCompatOptions = true;
//                     switch (options.Job)
//                     {
//                         case CSG.Adapter.Configuration.Job.FactData:
//                             string[] factJobStrings = args[1].Split(":");
//                             var factJobs = new List<CSG.Adapter.Configuration.FactDataJob>();
//                             foreach (var factJobStr in factJobStrings)
//                             {
//                                 CSG.Adapter.Configuration.FactDataJob factJob;
//                                 if (Enum.TryParse<CSG.Adapter.Configuration.FactDataJob>(factJobStr, true, out factJob))
//                                 {
//                                     factJobs.Add(factJob);
//                                 }
//                                 else
//                                 {
//                                     Console.WriteLine(
//                                         "Invalid command line parameters, unknown fact data job {0}", factJobStr);
//                                     options.Job = CSG.Adapter.Configuration.Job.Information;
//                                 }
//                             }
//                             options.Preferences.FactDataJobs = factJobs.ToArray<CSG.Adapter.Configuration.FactDataJob>();
//                             break;
//                         default:
//                             int monthOffset;
//                             if (int.TryParse(args[1], out monthOffset))
//                                 options.Preferences.OffsetMonths = monthOffset;
//                             break;
//                     }
//                 }
//             }
//             else
//             {
//                 Console.WriteLine("Invalid command line parameters");
//                 options.Job = CSG.Adapter.Configuration.Job.Information;
//             }
//         }

//         if (usingCompatOptions)
//             _legacyUsage += "OPTIONS_V3_COMPAT,";

// #endif
#if MYSQL
        if (options.Database.Type == CSG.Adapter.Configuration.DatabaseType.MySQL)
            _legacyUsage += "MYSQL,";

#endif
        options.Job ??= CSG.Adapter.Configuration.Job.Information;
        if (options.Database.Port == 0)
        {
            switch (options.Database.Type)
            {
                case CSG.Adapter.Configuration.DatabaseType.MSSQL:
                    options.Database.Port = 1433;
                    break;
                case CSG.Adapter.Configuration.DatabaseType.MySQL:
                    options.Database.Port = 3306;
                    break;
                case CSG.Adapter.Configuration.DatabaseType.PostgreSQL:
                    options.Database.Port = 5432;
                    break;
                case CSG.Adapter.Configuration.DatabaseType.Snowflake:
                    options.Database.Port = 443;
                    break;
            }
        }

        AppTelemetryInitializer.Configure(
            userAccountId: "Pre-initialisation OAuth client ID",
            userId: options.GenesysApi?.ClientId ?? "",
            job: options.Job?.ToString() ?? string.Empty);
        ServiceCollection serviceCollection = new ServiceCollection();
        serviceCollection.AddSingleton(configuration);
        if (options.Preferences.Telemetry)
        {
            serviceCollection
                .AddSingleton<ITelemetryInitializer, AppTelemetryInitializer>()
                .AddApplicationInsightsTelemetryProcessor<TelemetrySuccessfulDependencyFilter>()
                .AddApplicationInsightsTelemetryWorkerService(telemetryOptions =>
                    {
                        telemetryOptions.ApplicationVersion = ApplicationVersion.InformationalVersion;
                        telemetryOptions.ConnectionString = azureMonitorConnectionString;
#if DEBUG
                        telemetryOptions.DeveloperMode = options.LogLevel < CSG.Adapter.Configuration.LogLevel.Information;
                        telemetryOptions.EnableDebugLogger = options.LogLevel < CSG.Adapter.Configuration.LogLevel.Information;
#else
                        telemetryOptions.DeveloperMode = false;
                        telemetryOptions.EnableDebugLogger = false;
#endif
                        // Log SQL and outgoing HTTP transactions.
                        telemetryOptions.EnableDependencyTrackingTelemetryModule = true;
                        // Reduce data reported
                        telemetryOptions.EnableAdaptiveSampling = true;
                        telemetryOptions.EnableAppServicesHeartbeatTelemetryModule = false;
                        telemetryOptions.EnableAzureInstanceMetadataTelemetryModule = false;
                        telemetryOptions.EnableDiagnosticsTelemetryModule = false;
                        telemetryOptions.EnableEventCounterCollectionModule = false;
                        telemetryOptions.EnableHeartbeat = false;
                        telemetryOptions.EnablePerformanceCounterCollectionModule = false;
                        telemetryOptions.EnableQuickPulseMetricStream = false;
                    });
            if (options.LogLevel < CSG.Adapter.Configuration.LogLevel.Information)
                serviceCollection
                    .AddApplicationInsightsTelemetryProcessor<TelemetryFailedDependencyLogger>();

        }
        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Is((Serilog.Events.LogEventLevel)((int)(options.LogLevel ?? CSG.Adapter.Configuration.LogLevel.Information)))
            .Enrich.FromLogContext()
            .Enrich.WithExceptionDetails()
            .WriteTo.Console(outputTemplate: "{Timestamp:yyyy-MM-dd HH:mm:ss} [{Level:u3}] {Message:lj}{NewLine}{Exception}")
            .CreateLogger();

        serviceCollection.AddLogging(builder => {
            builder.AddSerilog(dispose: true);
            builder.SetMinimumLevel((LogLevel)((int)(options.LogLevel ?? CSG.Adapter.Configuration.LogLevel.Information)));
        });

        ServiceProvider serviceProvider = serviceCollection.BuildServiceProvider();

        // Initialize the service provider locator
        StandardUtils.ServiceProviderLocator.Initialize(serviceProvider);

        Console.WriteLine("=========================================================================");
        Console.WriteLine("Genesys Cloud Data Adapter v{0}", ApplicationVersion.MajorMinorPatch);
        Console.WriteLine("=========================================================================");

        _logger = serviceProvider.GetRequiredService<ILogger<Program>>();
        _telemetry = serviceProvider.GetService<TelemetryClient>();

#if DEBUG
        _logger.LogInformation("App:Init: Version {version} ({config})", ApplicationVersion.InformationalVersion, "debug");
#else
        _logger.LogInformation("App:Init: Version {version}", ApplicationVersion.InformationalVersion);
#endif
        _logger.LogInformation("App:Init: Running on {os} {rid} {framework}",
            ApplicationEnvironment.OSDescription,
            ApplicationEnvironment.RuntimeIdentifier,
            ApplicationEnvironment.FrameworkDescription
        );

        _logger.LogInformation("Configured culture: {0}", CultureInfo.CurrentCulture.Name);

        var gcUtils = new GenesysCloudUtils.GCUtils(_logger).Initialize(options.GenesysApi);
        CSG.Adapter.Genesys.Schema.V2.Response.Organization.Me organisationMe = gcUtils.GetOrganization();

        _logger.LogInformation("App:Init: Configured culture: {Culture}", CultureInfo.CurrentCulture.Name);
        _logger.LogInformation("App:Config: Genesys Cloud Client ID {ClientId}, endpoint {Endpoint}, orgName {OrgName}",
            options.GenesysApi?.ClientId ?? "<not set>",
            options.GenesysApi?.Endpoint,
            organisationMe.name ?? "<not set>");
        _logger.LogInformation("{type} database {name} at {address}:{port}, schema {schema}, user {user}",
            options.Database?.Type,
            options.Database?.Name ?? "<not set>",
            options.Database?.Address ?? "<not set>",
            options.Database?.Port,
            options.Database?.Schema ?? "<not set>",
            options.Database?.User ?? "<not set>");

        if (options.Job == null || options.Job == CSG.Adapter.Configuration.Job.Information)
        {
            ShowUsageAndInformation(options, defaultOptions, environmentVariablePrefix);
            return;
        }
        if (options.GenesysApi == null
            || options.Database == null
            || string.IsNullOrEmpty(options.GenesysApi.ClientId)
            || string.IsNullOrEmpty(options.GenesysApi.ClientSecret)
            || string.IsNullOrEmpty(options.Database.Address)
            || string.IsNullOrEmpty(options.Database.User)
            || string.IsNullOrEmpty(options.Database.Password)
            || new StandardUtils.Secret(options.GenesysApi.ClientId, options.GenesysApi.ClientSecret).IsEncrypted == false
            || new StandardUtils.Secret(options.GenesysApi.ClientId, options.Database.Password).IsEncrypted == false)
        {
            _logger.LogWarning("App:Config: Not all required options are set");
            ShowUsageAndInformation(options, defaultOptions, environmentVariablePrefix);
        }
        if (options.GenesysApi == null || options.Database == null)
            return;
        if (string.IsNullOrEmpty(options.GenesysApi.ClientId))
        {
            throw new ArgumentNullException(
                nameof(CSG.Adapter.Configuration.GenesysApi) + ":" + nameof(CSG.Adapter.Configuration.GenesysApi.ClientId),
                "Genesys Cloud OAuth client ID is not set"
            );
        }
        if (string.IsNullOrEmpty(options.GenesysApi.ClientSecret))
        {
            throw new ArgumentNullException(
                nameof(CSG.Adapter.Configuration.GenesysApi) + ":" + nameof(CSG.Adapter.Configuration.GenesysApi.ClientSecret),
                "Genesys Cloud OAuth client secret is not set"
            );
        }
        if (options.GenesysApi.Endpoint == null)
        {
            throw new ArgumentNullException(
                nameof(CSG.Adapter.Configuration.GenesysApi) + ":" + nameof(CSG.Adapter.Configuration.GenesysApi.Endpoint),
                "Genesys Cloud API endpoint"
            );
        }
// #if OPTIONS_V3_COMPAT
//         CSG.Adapter.Compatability.LegacyOptions.SetOption("CSG_CUSTOMERKEYID",
//             string.IsNullOrEmpty(legacyOptions.CustomerKeyId)
//                 ? options.GenesysApi.ClientId
//                 : legacyOptions.CustomerKeyId
//         );
// #else
        CSG.Adapter.Compatability.LegacyOptions.SetOption("CSG_CUSTOMERKEYID", options.GenesysApi.ClientId);
// #endif
        CSG.Adapter.Compatability.LegacyOptions.SetOption("CSG_GENESYS_USERID", options.GenesysApi.ClientId);
        CSG.Adapter.Compatability.LegacyOptions.SetOption("CSG_GENESYS_SECRET",
            new StandardUtils.Secret(options.GenesysApi.ClientId, options.GenesysApi.ClientSecret).PlainText);
        CSG.Adapter.Compatability.LegacyOptions.SetOption("CSG_GENESYS_URL", options.GenesysApi.Endpoint.ToString().TrimEnd('/'));

        _timeZone = TimeZoneInfo.FindSystemTimeZoneById(options.Preferences.TimeZone ?? "");
        CSG.Adapter.Compatability.LegacyOptions.SetOption("DateTimeZone", options.Preferences.TimeZone);

        CSG.Adapter.Compatability.LegacyOptions.SetOption("MaxSyncSpan", options.Preferences.MaxSyncSpan?.ToString());
        CSG.Adapter.Compatability.LegacyOptions.SetOption("LookBackSpan", options.Preferences.LookBackSpan?.ToString());
        CSG.Adapter.Compatability.LegacyOptions.SetOption("Job", options.Job.ToString());

        // Set granularity option if provided
        if (!string.IsNullOrEmpty(options.Preferences.Granularity))
        {
            // Validate that granularity is in ISO-8601 duration format
            if (!options.Preferences.Granularity.StartsWith("P"))
            {
                _logger.LogWarning("Granularity value '{0}' is not in ISO-8601 duration format (should start with 'P'). Using default value.", options.Preferences.Granularity);
            }
            else
            {
                // Check if granularity is at least 1 minute
                try
                {
                    TimeSpan duration = System.Xml.XmlConvert.ToTimeSpan(options.Preferences.Granularity);
                    if (duration.TotalMinutes < 1)
                    {
                        _logger.LogWarning("Granularity value '{0}' is less than minimum of 1 minute. Using PT1M instead.", options.Preferences.Granularity);
                        options.Preferences.Granularity = "PT1M";
                    }
                }
                catch (Exception)
                {
                    _logger.LogWarning("Granularity value '{0}' is not a valid ISO-8601 duration. Using default value.", options.Preferences.Granularity);
                    options.Preferences.Granularity = null;
                }
            }

            // Set the option if valid
            if (!string.IsNullOrEmpty(options.Preferences.Granularity))
            {
                CSG.Adapter.Compatability.LegacyOptions.SetOption("interval", options.Preferences.Granularity);
            }
        }

        AppTelemetryInitializer.Configure(
            userAccountId: organisationMe.name ?? "error",
            userId: options.GenesysApi?.ClientId ?? "error",
            authenticatedUserId: organisationMe.id ?? "error",
            job: options.Job.ToString() ?? string.Empty);

        if (string.IsNullOrEmpty(options.Database.Address))
        {
            throw new ArgumentNullException(
                nameof(CSG.Adapter.Configuration.Database) + ":" + nameof(CSG.Adapter.Configuration.Database.Address),
                "Database address is not set"
            );
        }
        if (string.IsNullOrEmpty(options.Database.User))
        {
            throw new ArgumentNullException(
                nameof(CSG.Adapter.Configuration.Database) + ":" + nameof(CSG.Adapter.Configuration.Database.User),
                "Database user is not set"
            );
        }
        if (string.IsNullOrEmpty(options.Database.Password))
        {
            throw new ArgumentNullException(
                nameof(CSG.Adapter.Configuration.Database) + ":" + nameof(CSG.Adapter.Configuration.Database.Password),
                "Database password is not set"
            );
        }
        if (string.IsNullOrEmpty(options.Database.Schema))
        {
            throw new ArgumentNullException(
                nameof(CSG.Adapter.Configuration.Database) + ":" + nameof(CSG.Adapter.Configuration.Database.Schema),
                "Database schema is not set"
            );
        }
        CSG.Adapter.Compatability.LegacyOptions.SetOption("CSG_SQLDATABASETYPE", options.Database.Type.ToString());
        CSG.Adapter.Compatability.LegacyOptions.SetOption("CSG_SQLDATABASESCHEMA", options.Database.Schema);

        // Build the connection string and initialize the connection manager
        var dbUtils = new DBUtils.DBUtils(_logger);
        string connectionString = dbUtils.BuildConnectionString(options.Database);
        CSG.Adapter.Compatability.LegacyOptions.SetOption("CSG_SQLCONNECTIONSTRING", connectionString);
        CSG.Adapter.Compatability.LegacyOptions.SetOption("CSG_SQLDATABASESHAREDDATABASE", options.Database.SharedDatabase.ToString());

        // Initialize the connection manager with the connection string
        DBUtils.ConnectionManager.Initialize(connectionString, options.Database.Type ?? CSG.Adapter.Configuration.DatabaseType.PostgreSQL, _logger);
        _logger.LogInformation("Database connection manager initialized with MaxPoolSize=50, Timeout=180");
        // Validate license
        _logger.LogInformation("App:License: Checking license for ID {CustomerId}",
            options.GenesysApi?.ClientId);
        var loggerFactory = serviceProvider.GetRequiredService<ILoggerFactory>();
        var licenseValidator = new CSG.Adapter.Licensing.LicenseValidator(
            options.GenesysApi?.ClientId, loggerFactory.CreateLogger<CSG.Adapter.Licensing.LicenseValidator>()
        );
        await licenseValidator.ValidateAsync();

        // Initialize the license manager with the validated license validator
        CSG.Adapter.Licensing.LicenseManager.Initialize(licenseValidator);

        _logger.LogInformation("Validated license for ID {CustomerId}.",
            options.GenesysApi?.ClientId);

        // Ensure that the job isn't already running on this system. If a job
        // runs concurrently unexpected behaviour will occur.
        // TODO: Inter-container locking
        //jobLock = ObtainLock(
        //    "GenesysCloudDataAdapter",
        //    options.Job.ToString() ?? nameof(CSG.Adapter.Configuration.Job.Install));
        //if (jobLock == null)
        //    throw new InvalidOperationException($"Another instance of the job {options.Job} is already running");

        //if (options.Job != CSG.Adapter.Configuration.Job.Install &&
        //    IsLockTaken("GenesysCloudDataAdapter", nameof(CSG.Adapter.Configuration.Job.Install)))
        //        throw new InvalidOperationException($"An install is currently running");

        _logger.LogInformation("App:Job: Starting job {Job}", options.Job);
        try
        {
            switch (options.Job)
            {
                case CSG.Adapter.Configuration.Job.Adherence:
                    new GCUpdateAdherenceData(_logger).UpdateGCAdherence();
                    break;

                case CSG.Adapter.Configuration.Job.Aggregation:
                    new GCUpdatePresenceData(_logger).UpdateGCUserPresenceData(options.Preferences.Backfill ?? false);
                    new GCUpdateInteractionData(_logger).UpdateGCUserInteractionData(options.Preferences.Backfill ?? false);
                    new GCUpdateInteractionData(_logger).UpdateGCQueueInteractionData(options.Preferences.Backfill ?? false);
                    break;

                case CSG.Adapter.Configuration.Job.Chat:
                    new GCUpdateChatData(_logger).UpdateGCChatData(
                        options.Preferences.RenameParticipantAttributeNames);
                    break;

                case CSG.Adapter.Configuration.Job.Message:
                    new GCUpdateMessageData(_logger).UpdateGCMessageData(
                        options.Preferences.RenameParticipantAttributeNames);
                    break;

                case CSG.Adapter.Configuration.Job.Evaluation:
                    new GCUpdateEvaluationData(_logger).UpdateGCEvaluationData(options.Preferences.Backfill ?? false);
                    break;

                case CSG.Adapter.Configuration.Job.EvaluationCatchup:
                    new GCUpdateEvaluationData(_logger).UpdateGCEvaluationCatchUp();
                    break;

                case CSG.Adapter.Configuration.Job.FactData:
                    if (options.Preferences.FactDataJobs == null)
                        options.Preferences.FactDataJobs = new CSG.Adapter.Configuration.FactDataJob[]{
                            CSG.Adapter.Configuration.FactDataJob.All};
                    if (options.GenesysApi is null)
                        throw new ArgumentNullException(nameof(options.GenesysApi));
                    await new GCUpdateFactTables(_logger)
                        .UpdateGCFactData(options.Preferences.FactDataJobs);
                    break;

                case CSG.Adapter.Configuration.Job.HeadCountForecast:
                    new GCDailyCheck(_logger).UpdateHeadcountForecast();
                    break;

                case CSG.Adapter.Configuration.Job.HoursBlockData:
                    new GCUpdateAdminData(_logger)
                        .UpdateHoursBlockData(options.Preferences.OffsetMonths, options.Preferences.Backfill ?? false);
                    break;

                case CSG.Adapter.Configuration.Job.Information:
                    // Handled earlier as part of parameter validation.
                    break;

                case CSG.Adapter.Configuration.Job.Install:
                    _logger?.LogInformation("Permissions Update is {Update}", options.Preferences?.Permissions?.Update == true ? "enabled" : "disabled");

                    new Install(_logger).InstallSystem(options);

                    if (options.Preferences.Permissions?.Update == true)
                    {
                        new GCUpdatePermissions(_logger, options.Preferences.Permissions.ForcedUpdate)
                            .UpdatePermissions(options.GenesysApi.ClientId);
                    }

                    break;

                case CSG.Adapter.Configuration.Job.Interaction:
                    new GCUpdateInteractionData(_logger)
                        .UpdateGCDetailInteractionData(
                            options.Preferences.BlockParticipantAttributes,
                            options.Preferences.RenameParticipantAttributeNames,
                            options.Preferences.Backfill ?? false).GetAwaiter().GetResult();
                    break;

                case CSG.Adapter.Configuration.Job.InteractionPresence:
                    new UserInteractionPresenceDetailedData(
                            _logger,
                            _timeZone,
                            options.Preferences.MaxSyncSpan)
                        .UpdateUserInteractionPresenceDetailedData();
                    break;

                case CSG.Adapter.Configuration.Job.OAuthUsage:
                    new GCUpdateAdminData(_logger)
                        .UpdateGCOauthUsageData(options.Preferences.OffsetMonths);
                    break;

                case CSG.Adapter.Configuration.Job.ODContactLists:
                    new GCUpdateOutboundDialingData(_logger).UpdateGCContactListData();
                    break;

                case CSG.Adapter.Configuration.Job.ODDetails:
                    new GCUpdateFactTables(_logger).UpdateODDetails();
                    break;

                case CSG.Adapter.Configuration.Job.OfferedForecast:
                    new GCDailyCheck(_logger).UpdateOfferedForecast();
                    break;

                case CSG.Adapter.Configuration.Job.PresenceDetail:
                    new GCUpdatePresenceData(_logger).UpdateGCUserPresenceDetailedData(options.Preferences.Backfill ?? false);
                    break;

                case CSG.Adapter.Configuration.Job.QueueMembership:
                    new GCUpdateAdminData(_logger).UpdateActQMembership();
                    break;

                case CSG.Adapter.Configuration.Job.Realtime:
                    new GCRealTime.GCRealTime(_logger, _telemetry).RunRealTime();
                    break;

                case CSG.Adapter.Configuration.Job.ScheduleDetails:
                    new GCUpdateWFMSchedData(_logger).UpdateScheduleDets();
                    break;

                case CSG.Adapter.Configuration.Job.Shrinkage:
                    new GCUpdateShrinkageData(_logger).UpdateGCShrinkageData();
                    break;

                case CSG.Adapter.Configuration.Job.Subscription:
                    new GCUpdateAdminData(_logger).UpdateGCSubsOverviewData();
                    break;

                case CSG.Adapter.Configuration.Job.SubsUsers:
                    new GCUpdateAdminData(_logger)
                        .UpdateGCSubHoursDetailData();
                    break;

                case CSG.Adapter.Configuration.Job.Survey:
                    new SurveyData(
                        _logger,
                        _timeZone,
                        options.Preferences.MaxSyncSpan
                    ).UpdateGCSurveyData();
                    break;

                case CSG.Adapter.Configuration.Job.SysConvUsage:
                    new GCUpdateAdminData(_logger)
                        .UpdateGCSystemCallUsageData(options.Preferences.OffsetMonths);
                    break;

                case CSG.Adapter.Configuration.Job.TimeOffReq:
                    new GCUpdateWFMSchedData(_logger).UpdateTimeOffRequests();
                    break;

                case CSG.Adapter.Configuration.Job.UserQueueAudit:
                    await new GCUpdateAdminData(_logger).UpdateQueueUserAuditData(options.Preferences.Backfill ?? false);
                    break;

                case CSG.Adapter.Configuration.Job.UserQueueMapping:
                    await new GCUpdateFactTables(_logger).UpdateGCUserQueueMapping();
                    break;

                case CSG.Adapter.Configuration.Job.VoiceAnalysis:
                    new GCUpdateInteractionData(_logger)
                        .UpdateGCVoiceAnalysisData(options.Preferences.Backfill ?? false);
                    break;

                case CSG.Adapter.Configuration.Job.WFMAudit:
                    new GCUpdateAdminData(_logger).UpdateWFMAuditData(options.Preferences.Backfill ?? false);
                    break;

                case CSG.Adapter.Configuration.Job.WFMSchedule:
                    new GCUpdateWFMSchedData(_logger).UpdateGCWFMSchedules(options.Preferences.Backfill ?? false);
                    break;
                case CSG.Adapter.Configuration.Job.LearningDataDetails:
                    new GCUpdateFactTables(_logger).LearningDataDetails();
                    break;

                case CSG.Adapter.Configuration.Job.Learning:
                    new GCUpdateLearningData(_logger).UpdateLearningData();
                    break;

                case CSG.Adapter.Configuration.Job.KnowledgeBaseDetails:
                    new GCUpdateFactTables(_logger).KnowledgeBaseDetails();
                    break;

                case CSG.Adapter.Configuration.Job.Knowledge:
                    new GCUpdateKnowledgeData(_logger).UpdateKnowledgeBase();
                    break;

                default:
                    throw new NotImplementedException($"Job '{options.Job}' is not implemented");
            }
        }
        catch (UnauthorizedAccessException ex) when (ex.Message.Contains("Permanent permission issue"))
        {
            _logger?.LogError(ex, "Permanent permission issue in Job '{Job}'. This requires administrator attention.", options.Job);
            // Exit with a specific error code for permission issues
            Environment.ExitCode = 3;
        }
        catch (UnauthorizedAccessException ex)
        {
            _logger?.LogError(ex, "Permission issue in Job '{Job}'. Check your API credentials and permissions.", options.Job);
            // Exit with a specific error code for permission issues
            Environment.ExitCode = 3;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Exception in Job '{Job}'", options.Job);
            // Set a general error code
            Environment.ExitCode = 1;
        }
        finally
        {
            // Close any open database connections at the end of the job
            try
            {
                // Log connection pool status
                DBUtils.ConnectionManager.LogPoolStatus();

                // Clear all connection pools to ensure clean shutdown
                DBUtils.ConnectionManager.ClearAllPools();
                _logger.LogInformation("App:Job: Cleared all database connection pools for job {Job}", options.Job);
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "App:Job: Failed to clear database connection pools");
            }
        }
    }


    static private void ShowUsageAndInformation(CSG.Adapter.Configuration.Options options,
        Dictionary<string, string?> defaultOptions, string environmentVariablePrefix)
    {
        if (_logger is null)
            throw new ApplicationException("Failed to initialise logging");

        // Width guide 80c .......10|.......20|.......30|.......40|.......50|.......60|.......70|.......80|"
        Console.WriteLine("Command line");
        Console.WriteLine("============");
        Console.WriteLine("  GenesysAdapter [{0}=<{0}>] [option=value] [...]", nameof(options.Job));
        Console.WriteLine();
        Console.WriteLine("Options");
        Console.WriteLine("=======");
        Console.WriteLine("  All options can be set in a configuration file, environment variable or passed");
        Console.WriteLine("  via the command line.");
        Console.WriteLine("  Some options have a default value if not specified, these values can be seen");
        Console.WriteLine("  below.");
        Console.WriteLine("  Option names are case-insensitive.");
        Console.WriteLine();
        Console.WriteLine("  Configuration file");
        Console.WriteLine("  ------------------");
        Console.WriteLine("    If appsettings.json exists in the working directory then it will be loaded.");
        Console.WriteLine("    Both environment variables and command line parameters will override values");
        Console.WriteLine("    set in the file.");
        Console.WriteLine();
        Console.WriteLine("    Example:");
        Console.WriteLine("      {");
        Console.WriteLine("        \"{0}\": {{", nameof(options.Database));
        Console.WriteLine("          \"{0}\":         \"{1}\",", nameof(options.Database.Type),
                                                            nameof(CSG.Adapter.Configuration.DatabaseType.PostgreSQL));
        Console.WriteLine("          \"{0}\":      \"server.example.com\",", nameof(options.Database.Address));
        Console.WriteLine("          \"{0}\":         5432,", nameof(options.Database.Port));
        Console.WriteLine("          \"{0}\":         \"postgres\",", nameof(options.Database.Name));
        Console.WriteLine("          \"{0}\":         \"postgres\",", nameof(options.Database.User));
        Console.WriteLine("          \"{0}\":     \"secret\"", nameof(options.Database.Password));
        Console.WriteLine("        },");
        Console.WriteLine("        \"{0}\": {{", nameof(options.GenesysApi));
        Console.WriteLine("          \"{0}\":     \"ccf050be-80a2-464a-8dad-60de53fc6a4c\",",
                                                                    nameof(options.GenesysApi.ClientId));
        Console.WriteLine("          \"{0}\": \"secret\",", nameof(options.GenesysApi.ClientSecret));
        Console.WriteLine("          \"{0}\":     \"https://api.mypurecloud.com.au\"",
                                                                    nameof(options.GenesysApi.Endpoint));
        Console.WriteLine("        },");
        Console.WriteLine("        \"{0}\":       \"{1}\"", nameof(options.LogLevel),
                                                                nameof(CSG.Adapter.Configuration.LogLevel.Information));
        Console.WriteLine("        \"{0}\": {{", nameof(options.Preferences));
        Console.WriteLine("          \"{0}\":     [\"All\"]", nameof(options.Preferences.FactDataJobs));
        Console.WriteLine("        },");
        Console.WriteLine("      }");
        Console.WriteLine();
        Console.WriteLine("  Environment variables");
        Console.WriteLine("  ---------------------");
        Console.WriteLine("    All options when set using environment variables must be prefixed with '{0}'.",
                                    environmentVariablePrefix);
        Console.WriteLine("    Command line parameters will override values set in environment variables.");
        Console.WriteLine("    The hierarchy delimiter is '__'.");
        Console.WriteLine();
        Console.WriteLine("    Example:");
        Console.WriteLine("      {0}{1}__{2}=\"ccf050be-80a2-464a-8dad-60de53fc6a4c\"",
                                    environmentVariablePrefix,
                                    nameof(options.GenesysApi),
                                    nameof(options.GenesysApi.ClientId));
        Console.WriteLine();
        Console.WriteLine("    Settings that take multiple values (i.e., {0}{1}__{2}) have a special syntax:",
                                    environmentVariablePrefix,
                                    nameof(options.Preferences), nameof(options.Preferences.FactDataJobs));
        Console.WriteLine("      {0}{1}__{2}__0=\"UserDetails\"",
                                    environmentVariablePrefix,
                                    nameof(options.Preferences), nameof(options.Preferences.FactDataJobs));
        Console.WriteLine("      {0}{1}__{2}__1=\"GroupDetails\"",
                                    environmentVariablePrefix,
                                    nameof(options.Preferences), nameof(options.Preferences.FactDataJobs));
        Console.WriteLine();
        // Width guide 80c .......10|.......20|.......30|.......40|.......50|.......60|.......70|.......80|"
        Console.WriteLine("  Command line arguments");
        Console.WriteLine("  ----------------------");
        Console.WriteLine("    The hierarchy delimiter is ':'.");
        Console.WriteLine("    Valid syntax for passing options are below. Any syntax may be used, but");
        Console.WriteLine("    mixing is not supported.");
        Console.WriteLine("      GenesysAdapter --option=value --option:suboption=value [...]");
        Console.WriteLine("      GenesysAdapter --option value --option:suboption value [...]");
        Console.WriteLine("      GenesysAdapter option=value option:suboption=value [...]");
        Console.WriteLine("      GenesysAdapter /option=value /option:suboption=value [...]");
        Console.WriteLine("      GenesysAdapter /option value /option:suboption value [...]");
        Console.WriteLine();
        Console.WriteLine("    Example:");
        Console.WriteLine("      GenesysAdapter {0}=\"{1}\"", nameof(options.Job), CSG.Adapter.Configuration.Job.Realtime);
        Console.WriteLine("      GenesysAdapter {0}:{1}=\"ccf050be-80a2-464a-8dad-60de53fc6a4c\"",
                                    nameof(options.GenesysApi),
                                    nameof(options.GenesysApi.ClientId));
        Console.WriteLine();

        Console.WriteLine("Available jobs ({0})", nameof(CSG.Adapter.Configuration.Job));
        Console.WriteLine("====================");
        foreach (CSG.Adapter.Configuration.Job entry in Enum.GetValues(typeof(CSG.Adapter.Configuration.Job)))
        {
            string description = entry.GetDescription();
            if (!string.IsNullOrEmpty(description))
                Console.WriteLine("  {0,-25}: {1}", entry, description);
        }
        Console.WriteLine();
        Console.WriteLine();
        Console.WriteLine("  (*) Requires Additional Configuration");
        Console.WriteLine();

        Console.WriteLine("Available fact data jobs ({0}:{1})",
            nameof(CSG.Adapter.Configuration.Preferences),
            nameof(CSG.Adapter.Configuration.Preferences.FactDataJobs));
        Console.WriteLine("===================================================");
        foreach (CSG.Adapter.Configuration.FactDataJob entry in Enum.GetValues(typeof(CSG.Adapter.Configuration.FactDataJob)))
        {
            string description = entry.GetDescription();
            if (!string.IsNullOrEmpty(description))
                Console.WriteLine("  {0,-25}: {1}", entry, description);
        }
        Console.WriteLine();

        Console.WriteLine("Available databases ({0}:{1})",
            nameof(CSG.Adapter.Configuration.Database),
            nameof(CSG.Adapter.Configuration.Database.Type));
        Console.WriteLine("===================================");
        foreach (CSG.Adapter.Configuration.DatabaseType entry in Enum.GetValues(typeof(CSG.Adapter.Configuration.DatabaseType)))
        {
            string description = entry.GetDescription();
            if (!string.IsNullOrEmpty(description))
                Console.WriteLine("  {0,-25}: {1}", entry, description);
        }
        Console.WriteLine();

        Console.WriteLine("Available logging levels ({0})",
            nameof(CSG.Adapter.Configuration.LogLevel));
        Console.WriteLine("===================================");
        foreach (CSG.Adapter.Configuration.LogLevel entry in Enum.GetValues(typeof(CSG.Adapter.Configuration.LogLevel)))
        {
            string description = entry.GetDescription();
            if (!string.IsNullOrEmpty(description))
                Console.WriteLine("  {0,-25}: {1}", entry, description);
        }
        Console.WriteLine();

        Console.WriteLine("Available permissions settings (Preferences:Permissions)");
        Console.WriteLine("========================================================");
        Console.WriteLine("  Update                  : Enable update permissions module");
        Console.WriteLine("  ForcedUpdate            : Force permission updates even when existing role data cannot be retrieved");
        Console.WriteLine();
        Console.WriteLine("Examples:");
        Console.WriteLine("  Enable permissions update:");
        Console.WriteLine("    GenesysAdapter Preferences:Permissions:Update=true");
        Console.WriteLine("  Enable forced permissions update:");
        Console.WriteLine("    GenesysAdapter Preferences:Permissions:ForcedUpdate=true");
        Console.WriteLine();

        Console.WriteLine("Current configuration");
        Console.WriteLine("=====================");
        var configError = false;
        foreach (var prop in options.GetType().GetProperties())
        {
            string description = prop
                .GetFirstAttributeOrNull<CSG.Adapter.Configuration.DescriptionAttribute>()?
                .Description ?? "";
            if (string.IsNullOrEmpty(description))
                continue;
            if (prop.GetValue(options) is null
                || prop.GetValue(options)!.GetType().Namespace is null
                || prop.GetValue(options)!.GetType().Namespace!.StartsWith("System"))
            {
                Console.WriteLine("  {0,-25}: {1} [default {2}]",
                                    prop.Name,
                                    description,
                                    defaultOptions.GetValueOrDefault(prop.Name, "<none>"));
                Console.WriteLine(
                    "  {0,-25}  {1}",
                    "",
                    prop.GetValue(options) ?? "<not set>");
            }
            else if (prop.GetValue(options)!.GetType().IsEnum)
            {
                Console.WriteLine("  {0,-25}: {1} [default {2}]",
                                    prop.Name,
                                    description,
                                    defaultOptions.GetValueOrDefault(prop.Name, "<none>"));
                Console.WriteLine(
                    "  {0,-25}  {1}",
                    "",
                    prop.GetValue(options) ?? "<not set>");
            }
            else
            {
                foreach (var subprop in prop.GetValue(options)!.GetType().GetProperties())
                {
                    description = subprop
                        .GetFirstAttributeOrNull<CSG.Adapter.Configuration.DescriptionAttribute>()?
                        .Description ?? "";
                    if (string.IsNullOrEmpty(description))
                        continue;
                    Console.WriteLine("  {0,-25}: {1} [default {2}]",
                                        prop.Name + ":" + subprop.Name,
                                        description,
                                        defaultOptions.GetValueOrDefault(prop.Name + ":" + subprop.Name, "<none>"));

                    object? rawValue = subprop.GetValue(prop.GetValue(options));
                    string value = "<not set>";
                    if (rawValue is CSG.Adapter.Configuration.RegexReplacement[])
                    {
                        var list = (CSG.Adapter.Configuration.RegexReplacement[])rawValue;
                        List<string> values = new();
                        foreach (var item in list)
                        {
                            values.Add($"'{item.Find}'->'{item.Replace}'");
                        }
                        value = string.Join(", ", values);
                    }
                    else if (rawValue != null && rawValue.GetType().IsArray)
                    {
                        Array array = (Array)rawValue;
                        object[] convertedArray = new object[array.Length];
                        for (int i = 0; i < array.Length; i++)
                        {
                            convertedArray[i] = array.GetValue(i) ?? "";
                        }
                        value = string.Join(", ", convertedArray);
                    }
                    else
                    {
                        value = rawValue?.ToString() ?? "<not set>";
                    }

                    // Special handling of credentials
                    if (value != "<not set>"
                        && (
                        (prop.Name == nameof(options.GenesysApi) && subprop.Name == nameof(options.GenesysApi.ClientSecret))
                        || (prop.Name == nameof(options.Database) && subprop.Name == nameof(options.Database.Password))
                        ))
                    {
                        var key = "";
                        switch (subprop.Name)
                        {
                            case nameof(options.GenesysApi.ClientSecret):
                                key = options.GenesysApi?.ClientId;
                                break;
                            case nameof(options.Database.Password):
                                key = options.Database?.User;
                                break;
                            default:
                                throw new NotImplementedException();
                        }
                        if (string.IsNullOrEmpty(key) || string.IsNullOrEmpty(value))
                        {
                            configError = true;
                            _logger.LogError(
                                "Option '{0}' is not set.",
                                prop.Name + ":" + subprop.Name
                            );
                        }
                        else
                        {
                            var secret = new StandardUtils.Secret(key ?? "", value.ToString() ?? "");
                            value = secret.Encrypted;
                            if (!secret.IsEncrypted)
                            {
                                configError = true;
                                _logger.LogError(
                                    "Option '{0}' is not encrypted, please update configuration with encrypted values.",
                                    prop.Name + ":" + subprop.Name
                                );
                            }
                        }
                    }
                    Console.WriteLine(
                        "  {0,-25}  {1}",
                        "",
                        value);
                }
            }
        }
        Console.WriteLine();

        // Add granularity information section
        Console.WriteLine("Granularity Option Usage");
        Console.WriteLine("=======================");
        Console.WriteLine("  The granularity option controls the time interval for Genesys Cloud API data retrieval.");
        Console.WriteLine("  It must be specified in ISO-8601 duration format.");
        Console.WriteLine();
        Console.WriteLine("  ISO-8601 Duration Format:");
        Console.WriteLine("  - P = Period (required prefix)");
        Console.WriteLine("  - T = Time separator (required before specifying hours, minutes, or seconds)");
        Console.WriteLine("  - nH = Number of hours");
        Console.WriteLine("  - nM = Number of minutes");
        Console.WriteLine("  - nS = Number of seconds");
        Console.WriteLine("  - nD = Number of days");
        Console.WriteLine();
        Console.WriteLine("  Examples:");
        Console.WriteLine("  - PT15M = 15 minutes");
        Console.WriteLine("  - PT30M = 30 minutes");
        Console.WriteLine("  - PT1H = 1 hour");
        Console.WriteLine("  - PT1H30M = 1 hour and 30 minutes");
        Console.WriteLine("  - P1D = 1 day");
        Console.WriteLine();
        Console.WriteLine("  Default Value and Minimum Granularity:");
        Console.WriteLine("  - Default: PT30M (30 minutes)");
        Console.WriteLine("  - Minimum: PT1M (1 minute)");
        Console.WriteLine();
        Console.WriteLine("  Setting Granularity:");
        Console.WriteLine("  1. Command Line: --Preferences:Granularity PT15M");
        Console.WriteLine("  2. Environment: {0}{1}__{2}=PT15M",
            environmentVariablePrefix,
            nameof(options.Preferences),
            nameof(options.Preferences.Granularity));
        Console.WriteLine("  3. Config File: \"Preferences\": { \"Granularity\": \"PT15M\" }");
        Console.WriteLine();

        if (configError)
        {
            _logger.LogCritical("Please fix errors listed above and try again");
            Environment.Exit(1);
        }
    }

    internal class AppTelemetryInitializer : ITelemetryInitializer
    {
        private static string _userAccountId = "";
        private static string _userId = "";
        private static string _authenticatedUserId = "";
        private static string _job = "";
        public static void Configure(string userAccountId = "", string userId = "", string authenticatedUserId = "", string job = "")
        {
            _userAccountId = userAccountId;
            _userId = userId;
            _authenticatedUserId = authenticatedUserId;
            _job = job;
        }

        public void Initialize(ITelemetry telemetry)
        {
            telemetry.Context.Session.Id = Guid.NewGuid().ToString();
            telemetry.Context.User.AccountId = _userAccountId;
            telemetry.Context.User.Id = _userId;
            telemetry.Context.User.AuthenticatedUserId = _authenticatedUserId;
            if (telemetry is ISupportProperties props)
            {
                props.Properties["Job"] = _job;
            }
        }
    }

    internal class TelemetrySuccessfulDependencyFilter : ITelemetryProcessor
    {
        private ITelemetryProcessor Next { get; set; }

        public TelemetrySuccessfulDependencyFilter(ITelemetryProcessor next)
        {
            this.Next = next;
        }

        public void Process(ITelemetry item)
        {
            var dependency = item as DependencyTelemetry;
            if (dependency != null                                          // Is a dependency
                && (dependency.Success ?? false)                            // Was successful
                && dependency.Duration <= TimeSpan.FromMilliseconds(250))   // Was <= 250 ms
            {
                switch (dependency.Type)
                {
                    // Dependency was successful and sub 250ms, don't log it.
                    case "Http":        // HTTP requests (i.e., Genesys API calls)
                    case "SQL":         // SQL requests
                    case "Web Service": // Web service requests (i.e., Control server connections)
                        return;         // Don't log

                    // Always log these successful dependencies
                    default:            // Unknown/other dependencies
                        break;          // Always log
                }
            }

            this.Next.Process(item);
        }
    }
    internal class TelemetryFailedDependencyLogger : ITelemetryProcessor
    {
        private readonly ITelemetryProcessor Next;

        public TelemetryFailedDependencyLogger(ITelemetryProcessor next)
        {
            this.Next = next;
        }
        public void Process(ITelemetry item)
        {
            var dependency = item as DependencyTelemetry;
            if (dependency != null && !(dependency.Success ?? false))
            {
                // Dependency was not successful
                _logger?.LogDebug(
                    "A dependency was not successful. Type {0}, result code {1}, target {2}, name {3}, duration {4}",
                    dependency.Type,
                    dependency.ResultCode,
                    dependency.Target,
                    dependency.Name,
                    dependency.Duration);
            }

            this.Next.Process(item);
        }
    }

    static private void UnhandledExceptionHandler(object sender, UnhandledExceptionEventArgs args)
    {
        Exception? ex = args.ExceptionObject as Exception;

        if (_logger != null)
        {
            _logger.LogCritical(ex, "A fatal error has occurred.");
        }
        else
        {
            Console.WriteLine("A fatal error has occurred.");
            Console.WriteLine(ex?.ToString());
            _telemetry?.TrackException(ex);
        }

        Environment.Exit(1);
    }

    static private void OnProcessExit()
    {
        if (_logger is null)
            Console.WriteLine("Application exiting with exit code {0}, running time {1}", Environment.ExitCode, _appTimer.Elapsed);
        else
            _logger.LogInformation("App:Exit: Application exiting with exit code {ExitCode}, running time {RunTime}",
                Environment.ExitCode, _appTimer.Elapsed);

        var props = new Dictionary<string, string>
            {
                {"ExitCode", Environment.ExitCode.ToString()},
                {"LegacyUsage", _legacyUsage.TrimEnd(',')}
            };
        var metrics = new Dictionary<string, double> { { "RunningTime", _appTimer.Elapsed.TotalSeconds } };
        _telemetry?.TrackEvent("Exit", props, metrics);

        _telemetry?.Flush();
        Serilog.Log.CloseAndFlush();
        // Azure Monitor flush needs a sleep, by design, in the documentation.
        // https://learn.microsoft.com/en-us/azure/azure-monitor/app/worker-service#net-corenet-framework-console-application
        Thread.Sleep(750);
    }
}
// spell-checker: ignore: sszzz
