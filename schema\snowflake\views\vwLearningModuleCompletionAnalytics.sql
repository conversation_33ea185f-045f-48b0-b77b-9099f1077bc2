-- Learning Module Completion Analytics View
-- Provides module-centric analytics including completion rates, performance metrics, and assignment statistics
-- Enables comprehensive learning analytics and reporting
--
-- PURPOSE:
-- This view aggregates learning data at the module level to provide comprehensive analytics
-- for learning administrators, managers, and stakeholders. It calculates key performance
-- indicators including completion rates, pass rates, and performance metrics across all
-- users assigned to each learning module.
--
-- USE CASES:
-- - Learning module effectiveness analysis and optimization
-- - Training program ROI and impact assessment
-- - Identification of challenging modules requiring additional support
-- - Completion rate tracking for compliance and certification programs
-- - Performance benchmarking across different learning modules
-- - Resource allocation and training schedule optimization
--
-- KEY METRICS:
-- - Assignment Statistics: Total assigned users, overdue users, active assignments
-- - Completion Metrics: Completed users, completion rate percentage
-- - Performance Analytics: Average assessment scores, completion percentages, duration
-- - Pass/Fail Analysis: Pass rates, failed users, assessment effectiveness
-- - Temporal Analysis: Assignment and completion date ranges
--
-- BUSINESS VALUE:
-- - Identifies high-performing vs. struggling learning modules
-- - Enables data-driven decisions for learning content optimization
-- - Supports compliance reporting and audit requirements
-- - Facilitates resource planning and capacity management

CREATE OR REPLACE VIEW vwLearningModuleCompletionAnalytics AS
SELECT 
    lm.id AS module_id,
    lm.name AS module_name,
    lm.description AS module_description,
    lm.type AS module_type,
    lma.state AS module_state,
    
    -- Assignment Statistics: Count unique users in different assignment states
    COUNT(DISTINCT lma.userid) AS total_assigned_users,  -- Total unique users assigned to this module
    COUNT(DISTINCT CASE WHEN lma.isOverdue = true THEN lma.userid END) AS overdue_users,  -- Users past due date
    COUNT(DISTINCT CASE WHEN lma.state = 'Assigned' THEN lma.userid END) AS active_assignments,  -- Currently assigned users
    
    -- Completion Statistics: Track completion progress and calculate rates
    COUNT(DISTINCT lar.userid) AS completed_users,  -- Users who have completion records
    -- Completion rate calculation: (completed users / total assigned users) * 100
    -- Uses NULLIF to prevent division by zero, returns NULL if no assignments
    ROUND(
        (COUNT(DISTINCT lar.userid)::FLOAT / NULLIF(COUNT(DISTINCT lma.userid), 0)) * 100, 2
    ) AS completion_rate_percentage,
    
    -- Performance Metrics: Calculate average performance indicators across all completions
    ROUND(AVG(lar.assessmentPercentageScore), 2) AS avg_assessment_score,  -- Average assessment score for completed users
    ROUND(AVG(lar.completionPercentage), 2) AS avg_completion_percentage,  -- Average overall completion percentage
    ROUND(AVG(lma.lengthInMinutes), 2) AS avg_duration_minutes,  -- Average time spent completing the module
    
    -- Pass/Fail Statistics: Count users who passed or failed based on assessment criteria
    -- Only includes users with both assessment scores and pass thresholds defined
    COUNT(DISTINCT CASE 
        WHEN lar.assessmentPercentageScore IS NOT NULL 
        AND lar.passPercent IS NOT NULL 
        AND lar.assessmentPercentageScore >= lar.passPercent 
        THEN lar.userid 
    END) AS passed_users,  -- Users who met or exceeded the pass threshold
    COUNT(DISTINCT CASE 
        WHEN lar.assessmentPercentageScore IS NOT NULL 
        AND lar.passPercent IS NOT NULL 
        AND lar.assessmentPercentageScore < lar.passPercent 
        THEN lar.userid 
    END) AS failed_users,  -- Users who scored below the pass threshold
    
    -- Pass Rate Calculation: (passed users / total assessed users) * 100
    -- Only considers users who have both assessment scores and pass thresholds
    -- Uses NULLIF to prevent division by zero when no assessments exist
    ROUND(
        (COUNT(DISTINCT CASE 
            WHEN lar.assessmentPercentageScore IS NOT NULL 
            AND lar.passPercent IS NOT NULL 
            AND lar.assessmentPercentageScore >= lar.passPercent 
            THEN lar.userid 
        END)::FLOAT / NULLIF(COUNT(DISTINCT CASE 
            WHEN lar.assessmentPercentageScore IS NOT NULL 
            AND lar.passPercent IS NOT NULL 
            THEN lar.userid 
        END), 0)) * 100, 2
    ) AS pass_rate_percentage,
    
    -- Temporal Analysis: Track assignment and completion date ranges for trend analysis
    MIN(lar.dateCreated) AS earliest_completion_date,   -- First completion date
    MAX(lar.dateModified) AS latest_completion_date     -- Most recent completion activity

FROM learningmodules lm
LEFT JOIN learningmoduleassignments lma ON lm.id = lma.moduleid
LEFT JOIN learningassignmentresults lar ON lma.userid = lar.userid AND lma.moduleid = lar.moduleid
WHERE lma.state != 'deleted'
GROUP BY lm.id, lm.name, lm.description, lm.type, lma.state
ORDER BY completion_rate_percentage DESC, lm.name;

-- Add comment explaining the view purpose and calculations
COMMENT ON VIEW vwLearningModuleCompletionAnalytics IS 
'Module-centric analytics view providing comprehensive completion rates, performance metrics, and assignment statistics.
Calculates completion rates, pass rates, average scores, and timing analytics per learning module.
Optimized for reporting and dashboard use cases. Uses userid+moduleid relationships for accurate cross-table joins.';
