﻿-- Learning Assignment to Results Correlation View
-- Provides direct correlation between assignments and their completion results
-- Enables tracking of assignment lifecycle from assignment to completion
--
-- PURPOSE:
-- This view creates a comprehensive correlation between learning module assignments and their
-- corresponding completion results, enabling detailed lifecycle tracking and performance analysis.
-- It serves as the foundation for understanding the complete journey from assignment to completion,
-- including timing analysis, performance metrics, and compliance tracking.
--
-- USE CASES:
-- - Assignment lifecycle tracking and audit trails
-- - Performance analysis and completion timing studies
-- - Compliance reporting and certification tracking
-- - Individual learning progress monitoring
-- - Manager oversight of team learning activities
-- - Learning analytics and trend analysis
-- - Resource planning and capacity management
--
-- KEY FEATURES:
-- - Complete assignment and result correlation via userid+moduleid
-- - Calculated lifecycle status and assessment results
-- - Timing analysis including days to completion and timeliness metrics
-- - User and module demographic information
-- - Performance indicators and assessment outcomes
-- - Comprehensive audit trail for learning activities
--
-- ANALYTICAL VALUE:
-- - Identifies patterns in learning completion behavior
-- - Enables optimization of assignment timing and deadlines
-- - Supports data-driven decisions for learning program improvements
-- - Facilitates compliance and audit reporting requirements

CREATE OR REPLACE VIEW vwLearningAssignmentCorrelation AS
SELECT
    -- Assignment Information
    lma.id AS assignment_id,
    lma.userid AS user_id,
    lma.moduleid AS module_id,
    lma.state AS assignment_state,
    lma.isOverdue AS is_assignment_overdue,
    lma.dateAssigned AS assignment_date,
    lma.dateDue AS assignment_due_date,
    lma.dateRecommendedForCompletion AS recommended_completion_date,
    lma.percentageScore AS assignment_percentage_score,

    -- User Information
    u.name AS user_name,
    u.email AS user_email,
    u.department AS user_department,
    u.title AS user_title,

    -- Module Information
    lm.name AS module_name,
    lm.description AS module_description,
    lm.type AS module_type,

    -- Results Information
    lar.id AS result_id,
    lar.assessmentPercentageScore AS result_assessment_score,
    lar.completionPercentage AS result_completion_percentage,
    lar.passPercent AS required_pass_percentage,
    lar.lengthInMinutes AS completion_duration_minutes,
    lar.dateCreated AS result_created_date,
    lar.dateModified AS result_modified_date,

    -- Calculated Fields: Derived metrics for lifecycle and performance analysis

    -- Lifecycle Status: Comprehensive status based on assignment and completion data
    -- Priority hierarchy: Completed > Overdue > In Progress > Unknown
    CASE
        WHEN lar.id IS NOT NULL THEN 'Completed'        -- Has completion record
        WHEN lma.isOverdue = true THEN 'Overdue'        -- Past due date without completion
        WHEN lma.state = 'Assigned' THEN 'In Progress'  -- Currently assigned and active
        ELSE 'Unknown'                                  -- Undefined or inactive state
    END AS lifecycle_status,

    -- Assessment Result: Pass/fail determination based on assessment criteria
    -- Includes 'Not Assessed' for assignments without assessment requirements
    CASE
        WHEN lar.assessmentPercentageScore IS NOT NULL AND lar.passPercent IS NOT NULL
        THEN CASE WHEN lar.assessmentPercentageScore >= lar.passPercent THEN 'Passed' ELSE 'Failed' END
        ELSE 'Not Assessed'  -- No assessment data available or required
    END AS assessment_result,

    -- Time to Completion Analysis: Calculate duration from assignment to completion
    -- Returns number of days between assignment and completion dates
    CASE
        WHEN lar.dateCreated IS NOT NULL AND lma.dateAssigned IS NOT NULL
        THEN DATEDIFF(DAY, lma.dateAssigned, lar.dateCreated)
        ELSE NULL  -- Missing assignment or completion dates
    END AS days_to_completion,

    -- Completion Timeliness: Determine if completion was on time or late
    -- Compares completion date against assignment due date
    CASE
        WHEN lar.dateCreated IS NOT NULL AND lma.dateDue IS NOT NULL
        THEN CASE WHEN lar.dateCreated > lma.dateDue THEN 'Completed Late' ELSE 'Completed On Time' END
        ELSE NULL  -- Missing completion or due dates
    END AS completion_timeliness

FROM learningmoduleassignments lma
INNER JOIN userdetails u ON lma.userid = u.id
INNER JOIN learningmodules lm ON lma.moduleid = lm.id
LEFT JOIN learningassignmentresults lar ON lma.userid = lar.userid AND lma.moduleid = lar.moduleid
WHERE u.state != 'deleted' AND lm.state != 'deleted'
ORDER BY lma.dateAssigned DESC, u.name, lm.name;

-- Add comment explaining the view purpose and key metrics
COMMENT ON VIEW vwLearningAssignmentCorrelation IS
'Direct correlation view between learning assignments and their completion results.
Tracks complete assignment lifecycle from assignment to completion with timing and performance metrics.
Includes calculated fields for lifecycle status, assessment results, completion timing, and timeliness analysis.
Uses userid+moduleid relationships to ensure accurate assignment-to-result correlation.';
