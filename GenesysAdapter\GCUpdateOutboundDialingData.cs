﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Data;
using System.Threading;
using System.Threading.Tasks;
using GCData;
using System.Net;
using GenesysCloudUtils;
using Microsoft.Extensions.Logging;
using CSG.Common.ExtensionMethods;
using StandardUtils;


namespace GenesysAdapter
{
    public class GCUpdateOutboundDialingData
    {
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public GCUpdateOutboundDialingData(ILogger? logger)
        {
            _logger = logger;
        }

        public async Task<Boolean> UpdateGCContactListDataAsync()
        {
            Boolean Successful = false;
            string SyncType = "odcontactlistdata";
            DateTime Start = DateTime.Now;

            _logger?.LogInformation("Job:Start: Beginning {SyncType} job", SyncType);

            DBUtil.Initialize();

            // Initialize GCData for sync date tracking
            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            Utils UCAUtils = new Utils();
            OutBoundDialingData OutBoundData = new OutBoundDialingData(_logger);
            OutBoundData.Initialize();

            _logger?.LogInformation("Retrieving contact lists from Genesys Cloud");
            DataTable ContactLists = await OutBoundData.GetContactListsFromCCAsync();

            // Only retrieve database data if we have contact lists to process
            DataTable DTODContactListData = null;
            if (ContactLists != null && ContactLists.Rows.Count > 0)
            {
                // Build optimized query to only fetch contact lists that are in scope
                var contactListIds = ContactLists.AsEnumerable()
                    .Select(row => $"'{row["contactlistid"]}'")
                    .Distinct()
                    .ToArray();

                if (contactListIds.Length > 0)
                {
                    string contactListFilter = string.Join(",", contactListIds);
                    string optimizedQuery = $"SELECT * FROM odcontactlistdata WHERE contactlistid IN ({contactListFilter})";

                    _logger?.LogInformation("Retrieving existing contact list data for {ContactListCount} contact lists", contactListIds.Length);
                    DTODContactListData = DBUtil.GetSQLTableData(optimizedQuery, "DTODContactListData");
                }
                else
                {
                    // Create empty table with same structure if no contact lists to process
                    DTODContactListData = DBUtil.CreateInMemTable("odcontactlistdata");
                }
            }

            if (ContactLists != null && ContactLists.Rows.Count > 0)
            {
                if(DBUtil.DBType==CSG.Adapter.Configuration.DatabaseType.Snowflake)
                {
                    UCAUtils.HandleSnowflakeColumnNames(ContactLists);
                }

                // Enhanced contact list isolation processing
                Successful = await ProcessContactListsWithIsolationAsync(ContactLists, DTODContactListData, SyncType);

                if (Successful)
                {
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
            }
            else
            {
                _logger?.LogInformation("ODContactLists: No contact lists data was returned from Genesys Cloud");
                Successful = true; // Empty result is still successful
            }

            // Update sync date even if no data was processed (to advance the sync window)
            if (Successful)
            {
                Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
            }

            TimeSpan elapsed = DateTime.Now - Start;
            _logger?.LogInformation("Job:End: Completed {SyncType} job in {Elapsed}", SyncType, elapsed);

            return Successful;
        }

        /// <summary>
        /// Process contact lists with proper isolation to prevent cross-contamination between contact lists.
        /// Each contact should have separate records for each contact list it belongs to.
        /// </summary>
        private async Task<bool> ProcessContactListsWithIsolationAsync(DataTable contactLists, DataTable existingData, string syncType)
        {
            try
            {
                // Group contacts by contact list ID to ensure proper isolation
                var contactListGroups = contactLists.AsEnumerable()
                    .GroupBy(row => row["contactlistid"]?.ToString() ?? "")
                    .Where(group => !string.IsNullOrEmpty(group.Key))
                    .ToList();

                _logger?.LogInformation("Processing {ContactListCount} contact lists with isolation", contactListGroups.Count);

                int totalProcessed = 0;
                int totalUpdated = 0;
                int totalAdded = 0;
                int totalSkipped = 0;

                // Collect all contacts that need to be written to database
                DataTable allContactsToWrite = contactLists.Clone();

                // Process contact lists sequentially to avoid database connection conflicts
                foreach (var contactListGroup in contactListGroups)
                {
                    var result = await ProcessSingleContactListForBatchAsync(contactListGroup, contactLists, existingData, allContactsToWrite);

                    if (!result.Success)
                    {
                        _logger?.LogError("Contact list {ContactListId} failed to process", result.ContactListId);
                        return false;
                    }

                    totalProcessed += result.TotalProcessed;
                    totalUpdated += result.Updated;
                    totalAdded += result.Added;
                    totalSkipped += result.Skipped;
                }

                // Write all contacts to database in a single batch operation
                if (allContactsToWrite.Rows.Count > 0)
                {
                    _logger?.LogInformation("Writing {TotalContacts} contacts to database in single batch operation", allContactsToWrite.Rows.Count);

                    bool writeSuccess = DBUtil.WriteDynamicSQLData(allContactsToWrite, "odcontactlistdata");
                    if (!writeSuccess)
                    {
                        _logger?.LogError("Failed to write contact batch to database");
                        return false;
                    }

                    _logger?.LogInformation("Successfully wrote {TotalContacts} contacts to database", allContactsToWrite.Rows.Count);
                }
                else
                {
                    _logger?.LogInformation("No contact changes detected - skipping database write");
                }

                _logger?.LogInformation("Contact list processing completed - Total: {Total}, Updated: {Updated}, Added: {Added}, Skipped: {Skipped}",
                    totalProcessed, totalUpdated, totalAdded, totalSkipped);

                return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing contact lists with isolation");
                return false;
            }
        }

        /// <summary>
        /// Result class for contact list processing
        /// </summary>
        private class ContactListProcessingResult
        {
            public bool Success { get; set; }
            public int TotalProcessed { get; set; }
            public int Updated { get; set; }
            public int Added { get; set; }
            public int Skipped { get; set; }
            public string ContactListId { get; set; } = "";
        }

        /// <summary>
        /// Process a single contact list asynchronously
        /// </summary>
        private async Task<ContactListProcessingResult> ProcessSingleContactListAsync(
            IGrouping<string, DataRow> contactListGroup,
            DataTable contactListsTemplate,
            DataTable existingData)
        {
            var result = new ContactListProcessingResult
            {
                ContactListId = contactListGroup.Key,
                Success = false
            };

            try
            {
                string contactListId = contactListGroup.Key;
                var contactsInList = contactListGroup.ToList();

                _logger?.LogDebug("Processing contact list {ContactListId} with {ContactCount} contacts",
                    contactListId, contactsInList.Count);

                // Create a DataTable for this specific contact list
                DataTable contactListData = contactListsTemplate.Clone();
                foreach (var contact in contactsInList)
                {
                    contactListData.ImportRow(contact);
                }

                // Get existing data for this specific contact list only
                var existingContactsForList = existingData.AsEnumerable()
                    .Where(row => row["contactlistid"]?.ToString() == contactListId)
                    .ToList();

                _logger?.LogDebug("Found {ExistingCount} existing contacts for contact list {ContactListId}",
                    existingContactsForList.Count, contactListId);

                // Process each contact in this list with strict keyid validation
                var contactsToProcess = new List<DataRow>();

                await Task.Run(() =>
                {
                    foreach (DataRow contact in contactListData.Rows)
                    {
                        string keyId = contact["keyid"]?.ToString() ?? "";
                        string contactId = contact["inin-outbound-id"]?.ToString() ?? "";

                        // Validate keyid format: should be contactlistid|contactid
                        string expectedKeyId = $"{contactListId}|{contactId}";
                        if (keyId != expectedKeyId)
                        {
                            _logger?.LogWarning("Invalid keyid format detected. Expected: {ExpectedKeyId}, Actual: {ActualKeyId}. Correcting...",
                                expectedKeyId, keyId);
                            contact["keyid"] = expectedKeyId;
                            keyId = expectedKeyId;
                        }

                        // Check if this exact record (same keyid) exists in the database
                        var existingRecord = existingContactsForList.FirstOrDefault(existing =>
                            existing["keyid"]?.ToString() == keyId);

                        if (existingRecord != null)
                        {
                            // Record exists - check if it needs updating
                            bool needsUpdate = false;
                            foreach (DataColumn column in contactListData.Columns)
                            {
                                if (column.ColumnName.ToUpper() == "UPDATED") continue;

                                if (!existingData.Columns.Contains(column.ColumnName))
                                {
                                    needsUpdate = true;
                                    break;
                                }

                                var newValue = contact[column.ColumnName] ?? DBNull.Value;
                                var existingValue = existingRecord[column.ColumnName] ?? DBNull.Value;

                                if (newValue == DBNull.Value) newValue = string.Empty;
                                if (existingValue == DBNull.Value) existingValue = string.Empty;

                                if (!newValue.Equals(existingValue))
                                {
                                    needsUpdate = true;
                                    break;
                                }
                            }

                            if (needsUpdate)
                            {
                                contactsToProcess.Add(contact);
                                result.Updated++;
                            }
                            else
                            {
                                result.Skipped++;
                            }
                        }
                        else
                        {
                            // New record
                            contactsToProcess.Add(contact);
                            result.Added++;
                        }
                    }
                });

                result.TotalProcessed = contactsInList.Count;

                // Process the contacts for this list if there are any changes
                if (contactsToProcess.Count > 0)
                {
                    DataTable processTable = contactListsTemplate.Clone();
                    foreach (var contact in contactsToProcess)
                    {
                        processTable.ImportRow(contact);
                    }

                    _logger?.LogDebug("Writing {ProcessCount} contacts for contact list {ContactListId} (Updated: {Updated}, Added: {Added})",
                        contactsToProcess.Count, contactListId, result.Updated, result.Added);

                    // Use Task.Run to make the database operation async
                    bool writeSuccess = await Task.Run(() => DBUtil.WriteDynamicSQLData(processTable, "odcontactlistdata"));

                    if (!writeSuccess)
                    {
                        _logger?.LogError("Failed to write contacts for contact list {ContactListId}", contactListId);
                        return result;
                    }
                }

                _logger?.LogDebug("Completed contact list {ContactListId}: {Total} total, {Updated} updated, {Added} added, {Skipped} skipped",
                    contactListId, result.TotalProcessed, result.Updated, result.Added, result.Skipped);

                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing contact list {ContactListId}", result.ContactListId);
                return result;
            }
        }

        /// <summary>
        /// Process a single contact list for batch writing (collects changes without writing to database)
        /// </summary>
        private async Task<ContactListProcessingResult> ProcessSingleContactListForBatchAsync(
            IGrouping<string, DataRow> contactListGroup,
            DataTable contactListsTemplate,
            DataTable existingData,
            DataTable allContactsToWrite)
        {
            var result = new ContactListProcessingResult
            {
                ContactListId = contactListGroup.Key,
                Success = false
            };

            try
            {
                string contactListId = contactListGroup.Key;
                var contactsInList = contactListGroup.ToList();

                _logger?.LogDebug("Processing contact list {ContactListId} with {ContactCount} contacts",
                    contactListId, contactsInList.Count);

                // Create a DataTable for this specific contact list
                DataTable contactListData = contactListsTemplate.Clone();
                foreach (var contact in contactsInList)
                {
                    contactListData.ImportRow(contact);
                }

                // Get existing data for this specific contact list only
                var existingContactsForList = existingData.AsEnumerable()
                    .Where(row => row["contactlistid"]?.ToString() == contactListId)
                    .ToList();

                _logger?.LogDebug("Found {ExistingCount} existing contacts for contact list {ContactListId}",
                    existingContactsForList.Count, contactListId);

                // Process each contact in this list with strict keyid validation
                await Task.Run(() =>
                {
                    foreach (DataRow contact in contactListData.Rows)
                    {
                        string keyId = contact["keyid"]?.ToString() ?? "";
                        string contactId = contact["inin-outbound-id"]?.ToString() ?? "";

                        // Validate keyid format: should be contactlistid|contactid
                        string expectedKeyId = $"{contactListId}|{contactId}";
                        if (keyId != expectedKeyId)
                        {
                            _logger?.LogWarning("Invalid keyid format detected. Expected: {ExpectedKeyId}, Actual: {ActualKeyId}. Correcting...",
                                expectedKeyId, keyId);
                            contact["keyid"] = expectedKeyId;
                            keyId = expectedKeyId;
                        }

                        // Check if this exact record (same keyid) exists in the database
                        var existingRecord = existingContactsForList.FirstOrDefault(existing =>
                            existing["keyid"]?.ToString() == keyId);

                        if (existingRecord != null)
                        {
                            // Record exists - check if it needs updating
                            bool needsUpdate = false;
                            foreach (DataColumn column in contactListData.Columns)
                            {
                                if (column.ColumnName.ToUpper() == "UPDATED") continue;

                                if (!existingData.Columns.Contains(column.ColumnName))
                                {
                                    needsUpdate = true;
                                    break;
                                }

                                var newValue = contact[column.ColumnName] ?? DBNull.Value;
                                var existingValue = existingRecord[column.ColumnName] ?? DBNull.Value;

                                if (newValue == DBNull.Value) newValue = string.Empty;
                                if (existingValue == DBNull.Value) existingValue = string.Empty;

                                if (!newValue.Equals(existingValue))
                                {
                                    needsUpdate = true;
                                    break;
                                }
                            }

                            if (needsUpdate)
                            {
                                allContactsToWrite.ImportRow(contact);
                                result.Updated++;
                            }
                            else
                            {
                                result.Skipped++;
                            }
                        }
                        else
                        {
                            // New record
                            allContactsToWrite.ImportRow(contact);
                            result.Added++;
                        }
                    }
                });

                result.TotalProcessed = contactsInList.Count;

                _logger?.LogDebug("Completed processing contact list {ContactListId}: {Total} total, {Updated} updated, {Added} added, {Skipped} skipped",
                    contactListId, result.TotalProcessed, result.Updated, result.Added, result.Skipped);

                result.Success = true;
                return result;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error processing contact list {ContactListId}", result.ContactListId);
                return result;
            }
        }

    }
}
