﻿using System;
using System.Linq;
using System.Data;
using GCData;
using System.Net;
using GenesysCloudUtils;
using Microsoft.Extensions.Logging;
using CSG.Common.ExtensionMethods;
using StandardUtils;


namespace GenesysAdapter
{
    public class GCUpdateOutboundDialingData
    {
        private DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public GCUpdateOutboundDialingData(ILogger? logger)
        {
            _logger = logger;
        }

        public Boolean UpdateGCContactListData()
        {
            Boolean Successful = false;
            string SyncType = "odcontactlistdata";
            DateTime Start = DateTime.Now;

            _logger?.LogInformation("Job:Start: Beginning {SyncType} job", SyncType);

            DBUtil.Initialize();

            // Initialize GCData to get lookback configuration
            GCGetData GCData = new GCGetData(_logger);
            GCData.Initialize(SyncType);

            // Calculate lookback date for contact list filtering with minimum date fallback
            DateTime effectiveSyncDate = GCData.DateToSyncFrom;
            DateTime minimumDate = new DateTime(2000, 1, 1, 0, 0, 0, DateTimeKind.Utc);

            // Use minimum date if no previous sync date exists
            if (effectiveSyncDate == DateTime.MinValue || effectiveSyncDate < minimumDate)
            {
                effectiveSyncDate = minimumDate;
                _logger?.LogInformation("ODContactLists: No previous sync date found, using minimum date {MinimumDate}Z for full historical sync",
                    minimumDate.ToString("s"));
            }

            DateTime lookbackDate = effectiveSyncDate.Subtract(GCData.LookBackSpan);

            _logger?.LogInformation("ODContactLists: Using lookback date {LookbackDate}Z for contact list filtering (sync from: {SyncDate}Z, lookback: {LookbackSpan})",
                lookbackDate.ToString("s"), effectiveSyncDate.ToString("s"), GCData.LookBackSpan);

            Utils UCAUtils = new Utils();
            OutBoundDialingData OutBoundData = new OutBoundDialingData(_logger);
            OutBoundData.Initialize();
            DataTable DTODContactListData = null;
            Thread dtodThread = new Thread(() =>
            {
                DTODContactListData = DBUtil.GetSQLTableData("select * from odcontactlistdata", "DTODContactListData");
            });
            dtodThread.Start();

            _logger?.LogInformation("Retrieving contact lists from Genesys Cloud");
            DataTable ContactLists = OutBoundData.GetContactListsFromCC(lookbackDate);

            dtodThread.Join();

            if (ContactLists != null && ContactLists.Rows.Count > 0)
            {
                if(DBUtil.DBType==CSG.Adapter.Configuration.DatabaseType.Snowflake)
                {
                    UCAUtils.HandleSnowflakeColumnNames(ContactLists);
                }
                List<object> idsToDelete = new List<object>();
                int updatedRowsCount = 0;
                int newRowCount = 0;
                foreach (DataRow row in ContactLists.Rows)
                {
                    DataRow[] matchingRows = DTODContactListData.Select($"keyid = '{row["keyid"]}'");

                    if (matchingRows.Length > 0)
                    {
                        DataRow matchingRow = matchingRows[0];

                        bool needsUpdate = false;

                        foreach (DataColumn column in ContactLists.Columns)
                        {
                             if (!matchingRow.Table.Columns.Contains(column.ColumnName))
                            {
                                needsUpdate = true;
                                updatedRowsCount ++;
                                break;
                            }
                            var contactListColumnValue = row[column.ColumnName];
                            var dataTableColumnValue = matchingRow[column.ColumnName];
                            if (contactListColumnValue == DBNull.Value)
                            {
                                contactListColumnValue = string.Empty;
                            }
                            if (dataTableColumnValue == DBNull.Value)
                            {
                                dataTableColumnValue = string.Empty;
                            }
                            if(!column.ColumnName.ToUpper().Equals("UPDATED") && !contactListColumnValue.Equals(dataTableColumnValue))
                            {
                                needsUpdate = true;
                                updatedRowsCount ++;
                                break;

                            }
                        }

                        if (!needsUpdate)
                        {
                            idsToDelete.Add(row["keyid"]);
                        }
                    }
                    else
                    {
                        newRowCount++;
                    }
                }

                _logger?.LogInformation(
                    "Contact list processing summary - GC: {GCCount}, DB: {DBCount}, Update: {UpdateCount}, Add: {AddCount}",
                    ContactLists.Rows.Count,
                    DTODContactListData.Rows.Count,
                    updatedRowsCount,
                    newRowCount);

                foreach (object idToDelete in idsToDelete)
                {
                    DataRow[] rowsToDelete = ContactLists.Select($"keyid = '{idToDelete}'");
                    foreach (DataRow rowToDelete in rowsToDelete)
                    {
                        ContactLists.Rows.Remove(rowToDelete);
                    }
                }


                Successful = DBUtil.WriteDynamicSQLData(ContactLists, "odcontactlistdata");

                if (Successful)
                {
                    Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
                }
            }
            else
            {
                _logger?.LogInformation("ODContactLists: No contact lists data was returned from Genesys Cloud (filtered by lookback date)");
                Successful = true; // Empty result due to filtering is still successful
            }

            // Update sync date even if no data was processed (to advance the sync window)
            if (Successful)
            {
                Successful = GCData.UpdateLastSuccessDate(DateTime.UtcNow, SyncType);
            }

            TimeSpan elapsed = DateTime.Now - Start;
            _logger?.LogInformation("Job:End: Completed {SyncType} job in {Elapsed}", SyncType, elapsed);

            return Successful;
        }

    }
}
