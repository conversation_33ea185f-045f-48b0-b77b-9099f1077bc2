# Automated Git Tagging for Genesys Adapter

This document describes the automated git tagging functionality that has been added to the Genesys Adapter CI pipeline.

## Overview

The CI pipeline now automatically creates and pushes git tags when builds are successful on the `master` branch. This provides automatic versioning and release tracking for production deployments.

## How It Works

### Azure Pipeline Approach

A new job called `CreateReleaseTag` has been added to the `azure-pipelines.yml` file that:

1. **Triggers only on master branch**: Only runs when code is merged to the master branch
2. **Depends on successful builds**: Requires Test, Publish, and DockerAll jobs to complete successfully
3. **Uses GitVersion for versioning**: Leverages the existing GitVersion tool to determine the semantic version
4. **Checks for existing tags**: Prevents duplicate tag creation if the version already exists
5. **Creates annotated tags**: Creates git tags with meaningful commit messages
6. **Pushes to remote**: Automatically pushes the new tag to the remote repository

### Nuke Build Target Approach

A new Nuke build target called `<PERSON>reate<PERSON><PERSON>aseTag` has been added to `build/Build.cs` that:

1. **Master branch only**: Uses `.OnlyWhenStatic(() => Repository.IsOnMainOrMasterBranch())`
2. **Server builds only**: Uses `.OnlyWhenStatic(() => IsServerBuild)`
3. **Depends on core targets**: Requires Test, Publish, and DockerAll to complete
4. **Uses semantic versioning**: Creates tags in the format `v{SemVer}` (e.g., `v3.49.1`)
5. **Duplicate prevention**: Checks existing tags to avoid conflicts
6. **Error handling**: Provides detailed logging and error handling

## Tag Format

Tags are created in the format: `v{Major}.{Minor}.{Patch}`

Examples:
- `v3.49.0`
- `v3.49.1`
- `v3.50.0`

## Usage

### Automatic (Recommended)

The tagging happens automatically when:
1. Code is merged to the `master` branch
2. All tests pass
3. Build and publish succeed
4. Docker image is built and pushed successfully

### Manual (Using Nuke)

You can also manually create a tag using the Nuke build system:

```bash
# Create a release tag manually
./build.cmd CreateReleaseTag

# Or on Linux/Mac
./build.sh CreateReleaseTag
```

## Configuration

### Pipeline Variables

The pipeline uses these built-in variables:
- `Build.SourceBranch`: To detect master branch
- `Build.SourceVersion`: For commit information
- `Build.BuildNumber`: As fallback version
- `System.AccessToken`: For git authentication

### GitVersion Configuration

The versioning is controlled by the existing `GitVersion.yml` file in the repository root.

## Benefits

1. **Consistent Versioning**: Automatic semantic versioning based on git history
2. **Release Tracking**: Easy identification of release points in git history
3. **Deployment Correlation**: Tags correspond to Docker images and releases
4. **Audit Trail**: Clear history of when releases were created
5. **Integration Ready**: Tags can be used by other tools for release notes, deployment tracking, etc.

## Troubleshooting

### Tag Creation Fails

If tag creation fails, check:
1. Git authentication is properly configured
2. The version doesn't already exist as a tag
3. The build is running on the master branch
4. All prerequisite jobs completed successfully

### Duplicate Tags

The system automatically checks for existing tags and skips creation if the tag already exists. This prevents pipeline failures due to duplicate tags.

### Version Issues

If GitVersion fails to determine a version:
1. Check the `GitVersion.yml` configuration
2. Ensure proper git history and branching
3. The system will fall back to using the build number

## Future Enhancements

Potential improvements that could be added:
1. **Release Notes Generation**: Automatically generate release notes from git commits
2. **GitHub/Azure DevOps Releases**: Create formal releases in the repository
3. **Notification Integration**: Send notifications when new releases are tagged
4. **Conditional Tagging**: Only tag when specific conditions are met (e.g., version bumps)

## Related Files

- `azure-pipelines.yml`: Contains the CI pipeline job
- `build/Build.cs`: Contains the Nuke build target
- `GitVersion.yml`: Controls version calculation
- `create_release_notes.ps1`: Existing script for release notes (can use the new tags)
