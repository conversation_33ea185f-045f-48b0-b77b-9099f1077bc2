using Newtonsoft.Json.Linq;
using System;
using System.Data;
using StandardUtils;
using System.Net;
using LearningModule = GenesysCloudDefLearningModules;
using LearningAssignment = GenesysCloudDefLearningModuleAssignments;
using LearningResult = GenesysCloudDefLearningAssignmentResults;
using Newtonsoft.Json;
using Microsoft.Extensions.Logging;
using System.ComponentModel;


namespace GenesysCloudUtils
{
    public class LearningDataConfig
    {


        public string CustomerKeyID { get; set; }
        public string GCApiKey { get; set; }
        public DataSet GCControlData { get; set; }
        private readonly GCUtils GCUtilities = new GCUtils();
        private readonly JsonUtils JsonActions = new JsonUtils();
        private readonly DBUtils.DBUtils DBUtil = new DBUtils.DBUtils();
        private readonly ILogger? _logger;

        public LearningDataConfig(ILogger? logger = null)
        {
            _logger = logger;
        }

        public void Initialize()
        {
            GCUtilities.Initialize();
            DBUtil.Initialize();
            Console.WriteLine("Initialization of GC Learning Modules Config ");
            CustomerKeyID = GCUtilities.CustomerKeyID;
            GCControlData = GCUtilities.GCControlData;
            GCApiKey = GCUtilities.GCApiKey;
        }

        public DataTable GetLearningModulesFromGC()
        {
            const string endpoint = "/api/v2/learning/modules";
            const string operation = "Get Learning Modules Data";

            _logger?.LogInformation("{Operation} - Starting", operation);
            Console.WriteLine(operation);

            DataTable LearningModules = DBUtil.CreateInMemTable("learningmodules");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            string fullEndpoint = URI + endpoint + "?pageSize=100";
            Console.Write("*");

            try
            {
                string JsonString = JsonActions.JsonReturnString(fullEndpoint, GCApiKey);

                // Check for permission errors (403 Forbidden)
                if (JsonString != null && JsonString.Contains("\"statusCode\": \"Forbidden\""))
                {
                    _logger?.LogWarning("Permission denied for {Operation}: {Endpoint}. This feature will be skipped but processing will continue.",
                        operation, endpoint);
                    return LearningModules; // Return empty table but don't fail the job
                }

                if (JsonString != null && JsonString.Length > 30)
                {
                    try
                    {
                        var LearningModuleList = JsonConvert.DeserializeObject<LearningModule.LearningModules>(JsonString,
                            new JsonSerializerSettings
                            {
                                NullValueHandling = NullValueHandling.Ignore
                            });

                        if (LearningModuleList?.entities == null)
                        {
                            _logger?.LogWarning("No entities found in learning modules response for endpoint: {Endpoint}", endpoint);
                            return LearningModules;
                        }

                        foreach (LearningModule.Entity Base in LearningModuleList.entities)
                        {
                            try
                            {
                                DataRow DrList = LearningModules.NewRow();

                                // Using standardized null value handling with ternary operator pattern and length validation
                                DrList["id"] = Base.id != null ? Base.id : DBNull.Value;

                                // Handle name with length validation
                                if (Base.name != null)
                                {
                                    string name = Base.name;
                                    if (name.Length > 100)
                                    {
                                        _logger?.LogWarning("Learning module {ModuleId} has name longer than 100 characters ({Length}). Truncating to fit database schema.",
                                            Base.id ?? "unknown", name.Length);
                                        name = name.Substring(0, 100);
                                    }
                                    DrList["name"] = name;
                                }
                                else
                                {
                                    DrList["name"] = DBNull.Value;
                                }

                                // Handle description with length validation to prevent database errors
                                if (Base.description != null)
                                {
                                    // Truncate description if it exceeds 500 characters and log a warning
                                    string description = Base.description;
                                    if (description.Length > 500)
                                    {
                                        _logger?.LogWarning("Learning module {ModuleId} has description longer than 500 characters ({Length}). Truncating to fit database schema.",
                                            Base.id ?? "unknown", description.Length);
                                        description = description.Substring(0, 500);
                                    }
                                    DrList["description"] = description;
                                }
                                else
                                {
                                    DrList["description"] = DBNull.Value;
                                }

                                // Handle source with length validation
                                if (Base.source != null)
                                {
                                    string source = Base.source;
                                    if (source.Length > 50)
                                    {
                                        _logger?.LogWarning("Learning module {ModuleId} has source longer than 50 characters ({Length}). Truncating to fit database schema.",
                                            Base.id ?? "unknown", source.Length);
                                        source = source.Substring(0, 50);
                                    }
                                    DrList["source"] = source;
                                }
                                else
                                {
                                    DrList["source"] = DBNull.Value;
                                }

                                DrList["version"] = Base.version != null ? Base.version : DBNull.Value; // version is VARCHAR(255), should be sufficient

                                // Handle externalId with length validation
                                if (Base.externalId != null)
                                {
                                    string externalId = Base.externalId;
                                    if (externalId.Length > 50)
                                    {
                                        _logger?.LogWarning("Learning module {ModuleId} has externalId longer than 50 characters ({Length}). Truncating to fit database schema.",
                                            Base.id ?? "unknown", externalId.Length);
                                        externalId = externalId.Substring(0, 50);
                                    }
                                    DrList["externalid"] = externalId;
                                }
                                else
                                {
                                    DrList["externalid"] = DBNull.Value;
                                }

                                // Handle type with length validation
                                if (Base.type != null)
                                {
                                    string type = Base.type;
                                    if (type.Length > 50)
                                    {
                                        _logger?.LogWarning("Learning module {ModuleId} has type longer than 50 characters ({Length}). Truncating to fit database schema.",
                                            Base.id ?? "unknown", type.Length);
                                        type = type.Substring(0, 50);
                                    }
                                    DrList["type"] = type;
                                }
                                else
                                {
                                    DrList["type"] = DBNull.Value;
                                }

                                // Handle DateTime values
                                DrList["datecreated"] = Base.dateCreated;
                                DrList["datemodified"] = Base.dateModified;

                                DrList["enforcecontentorder"] = Base.enforceContentOrder;

                                // Handle nullable value types
                                DrList["completiontimeindays"] = Base.completionTimeInDays.HasValue ?
                                    (object)Base.completionTimeInDays.Value : DBNull.Value;
                                DrList["lengthinminutes"] = Base.lengthInMinutes.HasValue ?
                                    (object)Base.lengthInMinutes.Value : DBNull.Value;

                                DrList["ispublished"] = Base.isPublished;
                                DrList["isArchived"] = Base.isArchived;

                                LearningModules.Rows.Add(DrList);
                            }
                            catch (Exception ex)
                            {
                                _logger?.LogError(ex, "Error processing learning module entity with ID: {ModuleId} - {ExceptionType}: {Message}",
                                    Base.id ?? "unknown", ex.GetType().Name, ex.Message);
                            }
                        }
                    }
                    catch (JsonException jsonEx)
                    {
                        _logger?.LogError(jsonEx, "JSON deserialization error in {Operation} for endpoint {Endpoint}: {ExceptionType}: {Message}",
                            operation, endpoint, jsonEx.GetType().Name, jsonEx.Message);
                        // Wrap in a more descriptive exception to be caught by outer catch
                        throw new InvalidOperationException($"Failed to deserialize response from {endpoint}", jsonEx);
                    }
                }
            }
            catch (UnauthorizedAccessException uaEx)
            {
                // Specific handling for permission/authorization errors
                _logger?.LogWarning(uaEx, "Permission denied for {Operation}: {Endpoint}. This feature will be skipped but processing will continue.",
                    operation, endpoint);
                // Return empty table but don't fail the job
                return LearningModules;
            }
            catch (Exception ex)
            {
                // Handle all other errors as critical
                _logger?.LogError(ex, "Critical error in {Operation} for endpoint {Endpoint}: {ExceptionType}: {Message}",
                    operation, endpoint, ex.GetType().Name, ex.Message);
                // Wrap in a more descriptive exception
                throw new InvalidOperationException($"Critical error in {operation} for endpoint {endpoint}", ex);
            }

            _logger?.LogInformation("{Operation} completed. Total records: {RecordCount}",
                operation, LearningModules.Rows.Count);

            return LearningModules;
        }
        public DataTable GetLearningModuleAssignmentsFromGC(DataTable LearningModules)
        {
            const string endpoint = "/api/v2/learning/assignments";
            const string operation = "Get Learning Module Assignment Data";

            _logger?.LogInformation("{Operation} - Starting", operation);
            Console.WriteLine(operation);

            DataTable LearningModuleAssignments = DBUtil.CreateInMemTable("learningmoduleassignments");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            Console.Write("*");

            try
            {
                _logger?.LogInformation("Retrieving learning assignments for {ModuleCount} learning modules", LearningModules.Rows.Count);

                foreach (DataRow moduleRow in LearningModules.Rows)
                {
                    try
                    {
                        string learningModuleId = moduleRow["id"].ToString();
                        string requestEndpoint = endpoint + "?pageSize=100&moduleId=" + learningModuleId;
                        string fullRequestUrl = URI + requestEndpoint;

                        string JsonString = JsonActions.JsonReturnString(fullRequestUrl, GCApiKey);

                        // Check for permission errors (403 Forbidden)
                        if (JsonString != null && JsonString.Contains("\"statusCode\": \"Forbidden\""))
                        {
                            _logger?.LogWarning("Permission denied for {Operation}: {Endpoint} with moduleId {ModuleId}. This module will be skipped but processing will continue.",
                                operation, endpoint, learningModuleId);
                            continue; // Skip this module but continue with others
                        }

                        if (JsonString != null && JsonString.Length > 30)
                        {
                            try
                            {
                                var LearningModuleAssignmentList = JsonConvert.DeserializeObject<LearningAssignment.LearningAssignment>(JsonString,
                                    new JsonSerializerSettings
                                    {
                                        NullValueHandling = NullValueHandling.Ignore
                                    });

                                if (LearningModuleAssignmentList?.entities == null)
                                {
                                    _logger?.LogDebug("No entities found in learning module assignment response for module ID: {ModuleId} at endpoint: {Endpoint}",
                                        learningModuleId, requestEndpoint);
                                    continue;
                                }

                                foreach (LearningAssignment.Entity Base in LearningModuleAssignmentList.entities)
                                {
                                    try
                                    {
                                        DataRow DrList = LearningModuleAssignments.NewRow();

                                        // Add module ID from the current query and extract user ID from assignment response
                                        DrList["moduleid"] = learningModuleId; // Set to current learning module being queried
                                        DrList["userid"] = Base.user?.id != null ? Base.user.id : DBNull.Value; // Extract user ID from assignment response

                                        // Ensure all string values are checked for null
                                        DrList["id"] = Base.id != null ? Base.id : DBNull.Value;
                                        DrList["version"] = Base.version != null ? Base.version : DBNull.Value;
                                        DrList["state"] = Base.state;
                                        DrList["isOverdue"] = Base.isOverdue;
                                        DrList["isRule"] = Base.isRule;
                                        DrList["isManual"] = Base.isManual;
                                        DrList["isPassed"] = Base.isPassed;
                                        DrList["isLatest"] = Base.isLatest;

                                        // Ensure all nullable value types are properly handled
                                        DrList["percentageScore"] = Base.percentageScore.HasValue ? (object)Base.percentageScore.Value : DBNull.Value;
                                        DrList["assessmentPercentageScore"] = Base.assessmentPercentageScore.HasValue ? (object)Base.assessmentPercentageScore.Value : DBNull.Value;
                                        DrList["assessmentCompletionPercentage"] = Base.assessmentCompletionPercentage.HasValue ? (object)Base.assessmentCompletionPercentage.Value : DBNull.Value;
                                        DrList["completionPercentage"] = Base.completionPercentage.HasValue ? (object)Base.completionPercentage.Value : DBNull.Value;
                                        DrList["lengthinminutes"] = Base.lengthInMinutes.HasValue ? (object)Base.lengthInMinutes.Value : DBNull.Value;

                                        // Handle DateTime values
                                        DrList["datecreated"] = Base.dateCreated;
                                        DrList["datemodified"] = Base.dateModified;

                                        // Handle nested objects
                                        if (Base.assessment != null)
                                        {
                                            DrList["assessmentid"] = Base.assessment.assessmentId != null ? Base.assessment.assessmentId : DBNull.Value;
                                            DrList["datesubmitted"] = Base.assessment.dateSubmitted ?? (object)DBNull.Value;
                                        }
                                        else
                                        {
                                            DrList["assessmentid"] = DBNull.Value;
                                            DrList["datesubmitted"] = DBNull.Value;
                                        }

                                        // Handle DateTime values with null check
                                        DrList["dateRecommendedForCompletion"] = Base.dateRecommendedForCompletion ?? (object)DBNull.Value;

                                        LearningModuleAssignments.Rows.Add(DrList);
                                    }
                                    catch (Exception ex)
                                    {
                                        _logger?.LogError(ex, "Error processing learning module assignment entity for module ID: {ModuleId} - {ExceptionType}: {Message}",
                                            learningModuleId, ex.GetType().Name, ex.Message);
                                    }
                                }
                            }
                            catch (JsonException jsonEx)
                            {
                                _logger?.LogError(jsonEx, "JSON deserialization error in {Operation} for module ID {ModuleId} at endpoint {Endpoint}: {ExceptionType}: {Message}",
                                    operation, learningModuleId, requestEndpoint, jsonEx.GetType().Name, jsonEx.Message);
                                // Continue with other modules - this is a per-module error
                            }
                        }
                    }
                    catch (UnauthorizedAccessException uaEx)
                    {
                        // Specific handling for permission/authorization errors for a specific module
                        string currentModuleId = moduleRow["id"]?.ToString() ?? "unknown";
                        _logger?.LogWarning(uaEx, "Permission denied for {Operation}: {Endpoint} with moduleId {ModuleId}. This module will be skipped but processing will continue.",
                            operation, endpoint, currentModuleId);
                        // Continue with other modules
                    }
                    catch (Exception ex)
                    {
                        // Log per-module errors but continue with others
                        string currentModuleId = moduleRow["id"]?.ToString() ?? "unknown";
                        _logger?.LogError(ex, "Error processing module ID {ModuleId} in {Operation}: {ExceptionType}: {Message}",
                            currentModuleId, operation, ex.GetType().Name, ex.Message);
                        // Continue with other modules
                    }
                }
            }
            catch (UnauthorizedAccessException uaEx)
            {
                // Specific handling for permission/authorization errors for the entire operation
                _logger?.LogWarning(uaEx, "Permission denied for {Operation}: {Endpoint}. This feature will be skipped but processing will continue.",
                    operation, endpoint);
                // Return what we have so far but don't fail the job
                return LearningModuleAssignments;
            }
            catch (Exception ex)
            {
                // Handle all other errors as critical
                _logger?.LogError(ex, "Critical error in {Operation} for endpoint {Endpoint}: {ExceptionType}: {Message}",
                    operation, endpoint, ex.GetType().Name, ex.Message);
                throw new InvalidOperationException($"Critical error in {operation} for endpoint {endpoint}", ex);
            }

            _logger?.LogInformation("{Operation} completed. Total records: {RecordCount}",
                operation, LearningModuleAssignments.Rows.Count);

            return LearningModuleAssignments;
        }

        public DataTable GetLearningAssignmentResultsFromGC(DataTable LearningModuleAssignments)
        {
            const string baseEndpoint = "/api/v2/learning/assignments/";
            const string operation = "Get Learning Assignment Results";
            const string unknownId = "unknown";

            _logger?.LogInformation("{Operation} - Starting", operation);
            Console.WriteLine(operation);

            DataTable LearningAssignmentResults = DBUtil.CreateInMemTable("learningassignmentresults");
            string URI = GCControlData.Tables["GCControlData"].Rows[0]["GC_URL"].ToString();
            Console.Write("*");

            try
            {
                // Validate input data table
                if (LearningModuleAssignments == null || LearningModuleAssignments.Rows.Count == 0)
                {
                    _logger?.LogInformation("No learning module assignments provided to process");
                    return LearningAssignmentResults;
                }

                foreach(DataRow row in LearningModuleAssignments.Rows)
                {
                    try
                    {
                        // Safely get the ID with null checking
                        if (row["id"] == null || row["id"] == DBNull.Value)
                        {
                            _logger?.LogWarning("Skipping learning module assignment with null ID");
                            continue;
                        }

                        string learningModuleAssignmentId = row["id"].ToString();
                        if (string.IsNullOrEmpty(learningModuleAssignmentId))
                        {
                            _logger?.LogWarning("Skipping learning module assignment with empty ID");
                            continue;
                        }

                        string requestEndpoint = baseEndpoint + learningModuleAssignmentId + "?expand=assessmentForm";
                        string fullRequestUrl = URI + requestEndpoint;

                        string JsonString = JsonActions.JsonReturnString(fullRequestUrl, GCApiKey);

                        // Check for permission errors (403 Forbidden)
                        if (JsonString != null && JsonString.Contains("\"statusCode\": \"Forbidden\""))
                        {
                            _logger?.LogWarning("Permission denied for {Operation}: {Endpoint} with assignmentId {AssignmentId}. This feature will be skipped but processing will continue.",
                                operation, requestEndpoint, learningModuleAssignmentId);
                            continue; // Skip this assignment but continue with others
                        }

                        if (JsonString != null && JsonString.Length > 30)
                        {
                            try
                            {
                                var LearningAssignmentResult = JsonConvert.DeserializeObject<LearningResult.LearningAssignmentResult>(JsonString,
                                    new JsonSerializerSettings
                                    {
                                        NullValueHandling = NullValueHandling.Ignore
                                    });

                                if (LearningAssignmentResult == null)
                                {
                                    _logger?.LogWarning("No data found in learning assignment result for assignment ID: {AssignmentId} at endpoint: {Endpoint}",
                                        learningModuleAssignmentId, requestEndpoint);
                                    continue;
                                }

                                DataRow DrList = LearningAssignmentResults.NewRow();

                                // Add user ID and module ID from the assignment data for consistency with learningmoduleassignments table
                                DrList["userid"] = row["userid"] != DBNull.Value ? row["userid"] : DBNull.Value; // Get userid from assignment data

                                // Get moduleid from assignment data or API response
                                object moduleIdValue = row["moduleid"] != DBNull.Value ? row["moduleid"] : DBNull.Value;
                                if (moduleIdValue == DBNull.Value && LearningAssignmentResult.module?.id != null)
                                {
                                    moduleIdValue = LearningAssignmentResult.module.id;
                                }
                                DrList["moduleid"] = moduleIdValue;

                                // Using standardized null value handling with ternary operator pattern
                                DrList["id"] = LearningAssignmentResult.id != null ? LearningAssignmentResult.id : DBNull.Value;

                                // Handle nullable value types
                                DrList["assessmentPercentageScore"] = LearningAssignmentResult.assessmentPercentageScore.HasValue ?
                                    (object)LearningAssignmentResult.assessmentPercentageScore.Value : DBNull.Value;

                                // Handle nested objects with null checks
                                DrList["passPercent"] = LearningAssignmentResult.assessmentForm?.passPercent.HasValue == true ?
                                    (object)LearningAssignmentResult.assessmentForm.passPercent.Value : DBNull.Value;

                                DrList["completionPercentage"] = LearningAssignmentResult.completionPercentage.HasValue ?
                                    (object)LearningAssignmentResult.completionPercentage.Value : DBNull.Value;

                                DrList["assessmentCompletionPercentage"] = LearningAssignmentResult.assessmentCompletionPercentage.HasValue ?
                                    (object)LearningAssignmentResult.assessmentCompletionPercentage.Value : DBNull.Value;

                                DrList["assessmentId"] = LearningAssignmentResult.assessment?.assessmentId != null ?
                                    LearningAssignmentResult.assessment.assessmentId : DBNull.Value;

                                DrList["moduleId"] = LearningAssignmentResult.module?.id != null ?
                                    LearningAssignmentResult.module.id : DBNull.Value;

                                DrList["assessmentFormId"] = LearningAssignmentResult.assessmentForm?.id != null ?
                                    LearningAssignmentResult.assessmentForm.id : DBNull.Value;

                                // Handle DateTime values with null checks
                                DrList["datecreated"] = LearningAssignmentResult.dateCreated != default(DateTime) ?
                                    (object)LearningAssignmentResult.dateCreated : DBNull.Value;
                                DrList["datemodified"] = LearningAssignmentResult.dateModified != default(DateTime) ?
                                    (object)LearningAssignmentResult.dateModified : DBNull.Value;

                                LearningAssignmentResults.Rows.Add(DrList);
                            }
                            catch (JsonException jsonEx)
                            {
                                _logger?.LogError(jsonEx, "JSON deserialization error in {Operation} for assignment ID {AssignmentId} at endpoint {Endpoint}: {ExceptionType}: {Message}",
                                    operation, learningModuleAssignmentId, requestEndpoint, jsonEx.GetType().Name, jsonEx.Message);
                                // Continue with other assignments - this is a per-assignment error
                            }
                        }
                    }
                    catch (UnauthorizedAccessException uaEx)
                    {
                        // Specific handling for permission/authorization errors for a specific assignment
                        string assignmentId = row["id"]?.ToString() ?? unknownId;
                        _logger?.LogWarning(uaEx, "Permission denied for {Operation}: {Endpoint} with assignmentId {AssignmentId}. This assignment will be skipped but processing will continue.",
                            operation, baseEndpoint, assignmentId);
                        // Continue with other assignments
                    }
                    catch (Exception ex)
                    {
                        // Log per-assignment errors but continue with others
                        string assignmentId = row["id"]?.ToString() ?? unknownId;
                        _logger?.LogError(ex, "Error processing assignment ID {AssignmentId} in {Operation}: {ExceptionType}: {Message}",
                            assignmentId, operation, ex.GetType().Name, ex.Message);
                        // Continue with other assignments
                    }
                }
            }
            catch (UnauthorizedAccessException uaEx)
            {
                // Specific handling for permission/authorization errors for the entire operation
                _logger?.LogWarning(uaEx, "Permission denied for {Operation}: {Endpoint}. This feature will be skipped but processing will continue.",
                    operation, baseEndpoint);
                // Return what we have so far but don't fail the job
                return LearningAssignmentResults;
            }
            catch (Exception ex)
            {
                // Handle all other errors as critical
                _logger?.LogError(ex, "Critical error in {Operation} for endpoint {Endpoint}: {ExceptionType}: {Message}",
                    operation, baseEndpoint, ex.GetType().Name, ex.Message);
                throw new InvalidOperationException($"Critical error in {operation} for endpoint {baseEndpoint}", ex); // Rethrow with context to signal a critical error
            }

            _logger?.LogInformation("{Operation} completed. Total records: {RecordCount}",
                operation, LearningAssignmentResults.Rows.Count);

            return LearningAssignmentResults;
        }
    }

}
