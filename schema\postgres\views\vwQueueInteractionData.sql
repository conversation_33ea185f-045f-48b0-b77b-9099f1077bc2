DROP VIEW IF EXISTS vwQueueInteractionData;

CREATE
OR REPLACE VIEW vwQueueInteractionData AS
SELECT
    queueinteractiondata.keyid,
    queueinteractiondata.startdate,
    queueinteractiondata.startDateltc,
    queueinteractiondata.direction,
    queueinteractiondata.queueid,
    queuedetails.name as queuename,
    queuedetails.divisionid AS queue_divisionid,
    divisiondetails.name as queue_divisionname,
    queueinteractiondata.mediatype,
    queueinteractiondata.wrapupcode,
    wrapupdetails.name as wrapupdesc,
    queueinteractiondata.talertcount,
    queueinteractiondata.talerttimesum,
    queueinteractiondata.talerttimesum / 86400.00 as talerttimesumDay,
    queueinteractiondata.talerttimemax,
    queueinteractiondata.talerttimemax / 86400.00 as talerttimemaxDay,
    queueinteractiondata.talerttimemin,
    queueinteractiondata.talerttimemin / 86400.00 as talerttimeminDay,
    queueinteractiondata.tansweredcount,
    queueinteractiondata.tansweredtimesum,
    queueinteractiondata.tansweredtimesum / 86400.00 as tansweredtimesumDay,
    queueinteractiondata.tansweredtimemax,
    queueinteractiondata.tansweredtimemax / 86400.00 as tansweredtimemaxDay,
    queueinteractiondata.tansweredtimemin,
    queueinteractiondata.tansweredtimemin / 86400.00 as tansweredtimeminDay,
    queueinteractiondata.ttalkcount,
    queueinteractiondata.ttalktimesum,
    queueinteractiondata.ttalktimesum / 86400.00 as ttalktimesumDay,
    queueinteractiondata.ttalktimemax,
    queueinteractiondata.ttalktimemax / 86400.00 as ttalktimemaxDay,
    queueinteractiondata.ttalktimemin,
    queueinteractiondata.ttalktimemin / 86400.00 as ttalktimeminDay,
    queueinteractiondata.ttalkcompletecount,
    queueinteractiondata.ttalkcompletetimesum,
    queueinteractiondata.ttalkcompletetimesum / 86400.00 as ttalkcompletetimesumDay,
    queueinteractiondata.ttalkcompletetimemax,
    queueinteractiondata.ttalkcompletetimemax / 86400.00 as ttalkcompletetimemaxDay,
    queueinteractiondata.ttalkcompletetimemin,
    queueinteractiondata.ttalkcompletetimemin / 86400.00 as ttalkcompletetimeminDay,
    queueinteractiondata.tnotrespondingcount,
    queueinteractiondata.tnotrespondingtimesum,
    queueinteractiondata.tnotrespondingtimesum / 86400.00 as tnotrespondingtimesumDay,
    queueinteractiondata.tnotrespondingtimemax,
    queueinteractiondata.tnotrespondingtimemax / 86400.00 as tnotrespondingtimemaxDay,
    queueinteractiondata.tnotrespondingtimemin,
    queueinteractiondata.tnotrespondingtimemin / 86400.00 as tnotrespondingtimeminDay,
    queueinteractiondata.theldcount,
    queueinteractiondata.theldtimesum,
    queueinteractiondata.theldtimesum / 86400.00 as theldtimesumDay,
    queueinteractiondata.theldtimemax,
    queueinteractiondata.theldtimemax / 86400.00 as theldtimemaxDay,
    queueinteractiondata.theldtimemin,
    queueinteractiondata.theldtimemin / 86400.00 as theldtimeminDay,
    queueinteractiondata.theldcompletecount,
    queueinteractiondata.theldcompletetimesum,
    queueinteractiondata.theldcompletetimesum / 86400.00 as theldcompletetimesumDay,
    queueinteractiondata.theldcompletetimemax,
    queueinteractiondata.theldcompletetimemax / 86400.00 as theldcompletetimemaxDay,
    queueinteractiondata.theldcompletetimemin,
    queueinteractiondata.theldcompletetimemin / 86400.00 as theldcompletetimeminDay,
    queueinteractiondata.thandlecount,
    queueinteractiondata.thandletimesum,
    queueinteractiondata.thandletimesum / 86400.00 as thandletimesumDay,
    queueinteractiondata.thandletimemax,
    queueinteractiondata.thandletimemax / 86400.00 as thandletimemaxDay,
    queueinteractiondata.thandletimemin,
    queueinteractiondata.thandletimemin / 86400.00 as thandletimeminDay,
    queueinteractiondata.tacwcount,
    queueinteractiondata.tacwtimesum,
    queueinteractiondata.tacwtimesum / 86400.00 as tacwtimesumDay,
    queueinteractiondata.tacwtimemax,
    queueinteractiondata.tacwtimemax / 86400.00 as tacwtimemaxDay,
    queueinteractiondata.tacwtimemin,
    queueinteractiondata.tacwtimemin / 86400.00 as tacwtimeminDay,
    queueinteractiondata.nconsult,
    queueinteractiondata.nconsulttransferred,
    queueinteractiondata.noutbound,
    queueinteractiondata.nerror,
    queueinteractiondata.ntransferred,
    queueinteractiondata.nblindtransferred,
    queueinteractiondata.nconnected,
    queueinteractiondata.noffered,
    queueinteractiondata.noversla,
    queueinteractiondata.tacdcount,
    queueinteractiondata.tacdtimesum,
    queueinteractiondata.tacdtimesum / 86400.00 as tacdtimesumDay,
    queueinteractiondata.tacdtimemax,
    queueinteractiondata.tacdtimemax / 86400.00 as tacdtimemaxDay,
    queueinteractiondata.tacdtimemin,
    queueinteractiondata.tacdtimemin / 86400.00 as tacdtimeminDay,
    queueinteractiondata.tdialingcount,
    queueinteractiondata.tdialingtimesum,
    queueinteractiondata.tdialingtimesum / 86400.00 as tdialingtimesumDay,
    queueinteractiondata.tdialingtimemax,
    queueinteractiondata.tdialingtimemax / 86400.00 as tdialingtimemaxDay,
    queueinteractiondata.tdialingtimemin,
    queueinteractiondata.tdialingtimemin / 86400.00 as tdialingtimeminDay,
    queueinteractiondata.tcontactingcount,
    queueinteractiondata.tcontactingtimesum,
    queueinteractiondata.tcontactingtimesum / 86400.00 as tcontactingtimesumDay,
    queueinteractiondata.tcontactingtimemax,
    queueinteractiondata.tcontactingtimemax / 86400.00 as tcontactingtimemaxDay,
    queueinteractiondata.tcontactingtimemin,
    queueinteractiondata.tcontactingtimemin / 86400.00 as tcontactingtimeminDay,
    queueinteractiondata.tvoicemailcount,
    queueinteractiondata.tvoicemailtimesum,
    queueinteractiondata.tvoicemailtimesum / 86400.00 as tvoicemailtimesumDay,
    queueinteractiondata.tvoicemailtimemax,
    queueinteractiondata.tvoicemailtimemax / 86400.00 as tvoicemailtimemaxDay,
    queueinteractiondata.tvoicemailtimemin,
    queueinteractiondata.tvoicemailtimemin / 86400.00 as tvoicemailtimeminDay,
    queueinteractiondata.tflowoutcount,
    queueinteractiondata.tflowouttimesum,
    queueinteractiondata.tflowouttimesum / 86400.00 as tflowouttimesumDay,
    queueinteractiondata.tflowouttimemax,
    queueinteractiondata.tflowouttimemax / 86400.00 as tflowouttimemaxDay,
    queueinteractiondata.tflowouttimemin,
    queueinteractiondata.tflowouttimemin / 86400.00 as tflowouttimeminDay,
    queueinteractiondata.twaitcount,
    queueinteractiondata.twaittimesum,
    queueinteractiondata.twaittimesum / 86400.00 as twaittimesumDay,
    queueinteractiondata.twaittimemax,
    queueinteractiondata.twaittimemax / 86400.00 as twaittimemaxDay,
    queueinteractiondata.twaittimemin,
    queueinteractiondata.twaittimemin / 86400.00 as twaittimeminDay,
    SUM(twaittimesum) / SUM(twaitcount) AS twaittimeavg,
    queueinteractiondata.tabandoncount,
    queueinteractiondata.tabandontimesum,
    queueinteractiondata.tabandontimesum / 86400.00 as tabandontimesumDay,
    queueinteractiondata.tabandontimemax,
    queueinteractiondata.tabandontimemax / 86400.00 as tabandontimemaxDay,
    queueinteractiondata.tabandontimemin,
    queueinteractiondata.tabandontimemin / 86400.00 as tabandontimeminDay,
    SUM(tabandontimesum) / SUM(tabandoncount) AS tabandontimeavg,
    (SUM(tabandontimesum) / SUM(tabandoncount)) / 86400.00 as tabandontimeavgDay,
    SUM(tabandoncount) / NULLIF(SUM(noffered), 0) as tabandoncountavg,
    queueinteractiondata.servicelevelnumerator,
    queueinteractiondata.serviceleveldenominator,
    SUM(servicelevelnumerator) / SUM(serviceleveldenominator) as servicelevelcalc,
    queueinteractiondata.av1count,
    queueinteractiondata.av1timesum,
    queueinteractiondata.av1timesum / 86400.00 as av1timesumDay,
    queueinteractiondata.av1timemax,
    queueinteractiondata.av1timemax / 86400.00 as av1timemaxDay,
    queueinteractiondata.av1timemin,
    queueinteractiondata.av1timemin / 86400.00 as av1timeminDay,
    queueinteractiondata.av2count,
    queueinteractiondata.av2timesum,
    queueinteractiondata.av2timesum / 86400.00 as av2timesumDay,
    queueinteractiondata.av2timemax,
    queueinteractiondata.av2timemax / 86400.00 as av2timemaxDay,
    queueinteractiondata.av2timemin,
    queueinteractiondata.av2timemin / 86400.00 as av2timeminDay,
    queueinteractiondata.av3count,
    queueinteractiondata.av3timesum,
    queueinteractiondata.av3timesum / 86400.00 as av3timesumDay,
    queueinteractiondata.av3timemax,
    queueinteractiondata.av3timemax / 86400.00 as av3timemaxDay,
    queueinteractiondata.av3timemin,
    queueinteractiondata.av3timemin / 86400.00 as av3timeminDay,
    queueinteractiondata.av4count,
    queueinteractiondata.av4timesum,
    queueinteractiondata.av4timesum / 86400.00 as av4timesumDay,
    queueinteractiondata.av4timemax,
    queueinteractiondata.av4timemax / 86400.00 as av4timemaxDay,
    queueinteractiondata.av4timemin,
    queueinteractiondata.av4timemin / 86400.00 as av4timeminDay,
    queueinteractiondata.av5count,
    queueinteractiondata.av5timesum,
    queueinteractiondata.av5timesum / 86400.00 as av5timesumDay,
    queueinteractiondata.av5timemax,
    queueinteractiondata.av5timemax / 86400.00 as av5timemaxDay,
    queueinteractiondata.av5timemin,
    queueinteractiondata.av5timemin / 86400.00 as av5timeminDay,
    queueinteractiondata.av6count,
    queueinteractiondata.av6timesum,
    queueinteractiondata.av6timesum / 86400.00 as av6timesumDay,
    queueinteractiondata.av6timemax,
    queueinteractiondata.av6timemax / 86400.00 as av6timemaxDay,
    queueinteractiondata.av6timemin,
    queueinteractiondata.av6timemin / 86400.00 as av6timeminDay,
    queueinteractiondata.av7count,
    queueinteractiondata.av7timesum,
    queueinteractiondata.av7timesum / 86400.00 as av7timesumDay,
    queueinteractiondata.av7timemax,
    queueinteractiondata.av7timemax / 86400.00 as av7timemaxDay,
    queueinteractiondata.av7timemin,
    queueinteractiondata.av7timemin / 86400.00 as av7timeminDay,
    queueinteractiondata.av8count,
    queueinteractiondata.av8timesum,
    queueinteractiondata.av8timesum / 86400.00 as av8timesumDay,
    queueinteractiondata.av8timemax,
    queueinteractiondata.av8timemax / 86400.00 as av8timemaxDay,
    queueinteractiondata.av8timemin,
    queueinteractiondata.av8timemin / 86400.00 as av8timeminDay,
    queueinteractiondata.av9count,
    queueinteractiondata.av9timesum,
    queueinteractiondata.av9timesum / 86400.00 as av9timesumDay,
    queueinteractiondata.av9timemax,
    queueinteractiondata.av9timemax / 86400.00 as av9timemaxDay,
    queueinteractiondata.av9timemin,
    queueinteractiondata.av9timemin / 86400.00 as av9timeminDay,
    queueinteractiondata.av10count,
    queueinteractiondata.av10timesum,
    queueinteractiondata.av10timesum / 86400.00 as av10timesumDay,
    queueinteractiondata.av10timemax,
    queueinteractiondata.av10timemax / 86400.00 as av10timemaxDay,
    queueinteractiondata.av10timemin,
    queueinteractiondata.av10timemin / 86400.00 as av10timeminDay,
    queueinteractiondata.oservicetarget,
    queueinteractiondata.tivrcount,
    queueinteractiondata.tivrtimesum,
    queueinteractiondata.tivrtimesum / 86400.00 as tivrtimesumDay,
    queueinteractiondata.tivrtimemax,
    queueinteractiondata.tivrtimemax / 86400.00 as tivrtimemaxDay,
    queueinteractiondata.tivrtimemin,
    queueinteractiondata.tivrtimemin / 86400.00 as tivrtimeminDay,
    queueinteractiondata.tshortabandoncount,
    queueinteractiondata.tshortabandontimesum,
    queueinteractiondata.tshortabandontimesum / 86400.00 as tshortabandontimesumDay,
    queueinteractiondata.tshortabandontimemax,
    queueinteractiondata.tshortabandontimemax / 86400.00 as tshortabandontimemaxDay,
    queueinteractiondata.tshortabandontimemin,
    queueinteractiondata.tshortabandontimemin / 86400.00 as tshortabandontimeminDay,
    queueinteractiondata.tuserresponsetimecount,
    queueinteractiondata.tuserresponsetimetimesum,
    queueinteractiondata.tuserresponsetimetimesum / 86400.00 as tuserresponsetimetimesumDay,
    queueinteractiondata.tuserresponsetimetimemax,
    queueinteractiondata.tuserresponsetimetimemax / 86400.00 as tuserresponsetimetimemaxDay,
    queueinteractiondata.tuserresponsetimetimemin,
    queueinteractiondata.tuserresponsetimetimemin / 86400.00 as tuserresponsetimetimeminDay,
    queueinteractiondata.tagentresponsetimecount,
    queueinteractiondata.tagentresponsetimetimesum,
    queueinteractiondata.tagentresponsetimetimesum / 86400.00 as tagentresponsetimetimesumDay,
    queueinteractiondata.tagentresponsetimetimemax,
    queueinteractiondata.tagentresponsetimetimemax / 86400.00 as tagentresponsetimetimemaxDay,
    queueinteractiondata.tagentresponsetimetimemin,
    queueinteractiondata.tagentresponsetimetimemin / 86400.00 as tagentresponsetimetimeminDay,
    queueinteractiondata.updated
FROM
    queueInteractiondata as queueinteractiondata
    left outer join queuedetails as queuedetails on queuedetails.id = queueinteractiondata.queueid
    left outer join wrapupdetails as wrapupdetails on wrapupdetails.id = queueinteractiondata.wrapupcode
    left outer join divisiondetails as divisiondetails on queuedetails.divisionid = divisiondetails.id
GROUP BY
    queueinteractiondata.keyid,
    queueinteractiondata.startdate,
    queueinteractiondata.startdateltc,
    queueinteractiondata.direction,
    queueinteractiondata.queueid,
    queuedetails.name,
    queuedetails.divisionid,
    divisiondetails.name,
    queueinteractiondata.mediatype,
    queueinteractiondata.wrapupcode,
    wrapupdetails.name,
    queueinteractiondata.talertcount,
    queueinteractiondata.talerttimesum,
    queueinteractiondata.talerttimemax,
    queueinteractiondata.talerttimemin,
    queueinteractiondata.tansweredcount,
    queueinteractiondata.tansweredtimesum,
    queueinteractiondata.tansweredtimemax,
    queueinteractiondata.tansweredtimemin,
    queueinteractiondata.ttalkcount,
    queueinteractiondata.ttalktimesum,
    queueinteractiondata.ttalktimemax,
    queueinteractiondata.ttalktimemin,
    queueinteractiondata.ttalkcompletecount,
    queueinteractiondata.ttalkcompletetimesum,
    queueinteractiondata.ttalkcompletetimemax,
    queueinteractiondata.ttalkcompletetimemin,
    queueinteractiondata.tnotrespondingcount,
    queueinteractiondata.tnotrespondingtimesum,
    queueinteractiondata.tnotrespondingtimemax,
    queueinteractiondata.tnotrespondingtimemin,
    queueinteractiondata.theldcount,
    queueinteractiondata.theldtimesum,
    queueinteractiondata.theldtimemax,
    queueinteractiondata.theldtimemin,
    queueinteractiondata.theldcompletecount,
    queueinteractiondata.theldcompletetimesum,
    queueinteractiondata.theldcompletetimemax,
    queueinteractiondata.theldcompletetimemin,
    queueinteractiondata.thandlecount,
    queueinteractiondata.thandletimesum,
    queueinteractiondata.thandletimemax,
    queueinteractiondata.thandletimemin,
    queueinteractiondata.tacwcount,
    queueinteractiondata.tacwtimesum,
    queueinteractiondata.tacwtimemax,
    queueinteractiondata.tacwtimemin,
    queueinteractiondata.nconsult,
    queueinteractiondata.nconsulttransferred,
    queueinteractiondata.noutbound,
    queueinteractiondata.nerror,
    queueinteractiondata.ntransferred,
    queueinteractiondata.nblindtransferred,
    queueinteractiondata.nconnected,
    queueinteractiondata.noffered,
    queueinteractiondata.noversla,
    queueinteractiondata.tacdcount,
    queueinteractiondata.tacdtimesum,
    queueinteractiondata.tacdtimemax,
    queueinteractiondata.tacdtimemin,
    queueinteractiondata.tdialingcount,
    queueinteractiondata.tdialingtimesum,
    queueinteractiondata.tdialingtimemax,
    queueinteractiondata.tdialingtimemin,
    queueinteractiondata.tcontactingcount,
    queueinteractiondata.tcontactingtimesum,
    queueinteractiondata.tcontactingtimemax,
    queueinteractiondata.tcontactingtimemin,
    queueinteractiondata.tvoicemailcount,
    queueinteractiondata.tvoicemailtimesum,
    queueinteractiondata.tvoicemailtimemax,
    queueinteractiondata.tvoicemailtimemin,
    queueinteractiondata.tflowoutcount,
    queueinteractiondata.tflowouttimesum,
    queueinteractiondata.tflowouttimemax,
    queueinteractiondata.tflowouttimemin,
    queueinteractiondata.twaitcount,
    queueinteractiondata.twaittimesum,
    queueinteractiondata.twaittimemax,
    queueinteractiondata.twaittimemin,
    queueinteractiondata.tabandoncount,
    queueinteractiondata.tabandontimesum,
    queueinteractiondata.tabandontimemax,
    queueinteractiondata.tabandontimemin,
    queueinteractiondata.servicelevelnumerator,
    queueinteractiondata.serviceleveldenominator,
    queueinteractiondata.av1count,
    queueinteractiondata.av1timesum,
    queueinteractiondata.av1timemax,
    queueinteractiondata.av1timemin,
    queueinteractiondata.av2count,
    queueinteractiondata.av2timesum,
    queueinteractiondata.av2timemax,
    queueinteractiondata.av2timemin,
    queueinteractiondata.av3count,
    queueinteractiondata.av3timesum,
    queueinteractiondata.av3timemax,
    queueinteractiondata.av3timemin,
    queueinteractiondata.av4count,
    queueinteractiondata.av4timesum,
    queueinteractiondata.av4timemax,
    queueinteractiondata.av4timemin,
    queueinteractiondata.av5count,
    queueinteractiondata.av5timesum,
    queueinteractiondata.av5timemax,
    queueinteractiondata.av5timemin,
    queueinteractiondata.av6count,
    queueinteractiondata.av6timesum,
    queueinteractiondata.av6timemax,
    queueinteractiondata.av6timemin,
    queueinteractiondata.av7count,
    queueinteractiondata.av7timesum,
    queueinteractiondata.av7timemax,
    queueinteractiondata.av7timemin,
    queueinteractiondata.av8count,
    queueinteractiondata.av8timesum,
    queueinteractiondata.av8timemax,
    queueinteractiondata.av8timemin,
    queueinteractiondata.av9count,
    queueinteractiondata.av9timesum,
    queueinteractiondata.av9timemax,
    queueinteractiondata.av9timemin,
    queueinteractiondata.av10count,
    queueinteractiondata.av10timesum,
    queueinteractiondata.av10timemax,
    queueinteractiondata.av10timemin,
    queueinteractiondata.oservicetarget,
    queueinteractiondata.tivrcount,
    queueinteractiondata.tivrtimesum,
    queueinteractiondata.tivrtimemax,
    queueinteractiondata.tivrtimemin,
    queueinteractiondata.tshortabandoncount,
    queueinteractiondata.tshortabandontimesum,
    queueinteractiondata.tshortabandontimemax,
    queueinteractiondata.tshortabandontimemin,
    queueinteractiondata.tuserresponsetimecount,
    queueinteractiondata.tuserresponsetimetimesum,
    queueinteractiondata.tuserresponsetimetimemax,
    queueinteractiondata.tuserresponsetimetimemin,
    queueinteractiondata.tagentresponsetimecount,
    queueinteractiondata.tagentresponsetimetimesum,
    queueinteractiondata.tagentresponsetimetimemax,
    queueinteractiondata.tagentresponsetimetimemin,
    queueinteractiondata.updated;

-- Add comments
COMMENT ON COLUMN vwQueueInteractionData.keyid IS 'Primary Key';
COMMENT ON COLUMN vwQueueInteractionData.startdate IS 'Start Date (UTC)';
COMMENT ON COLUMN vwQueueInteractionData.startDateltc IS 'Start Date (LTC)';
COMMENT ON COLUMN vwQueueInteractionData.direction IS 'Direction';
COMMENT ON COLUMN vwQueueInteractionData.queueid IS 'Queue GUID';
COMMENT ON COLUMN vwQueueInteractionData.queuename IS 'Queue Name';
COMMENT ON COLUMN vwQueueInteractionData.queue_divisionid IS 'Queue Division GUID';
COMMENT ON COLUMN vwQueueInteractionData.queue_divisionname IS 'Queue Division Name';
COMMENT ON COLUMN vwQueueInteractionData.mediatype IS 'Media Type';
COMMENT ON COLUMN vwQueueInteractionData.wrapupcode IS 'Wrap-up Code';
COMMENT ON COLUMN vwQueueInteractionData.wrapupdesc IS 'Wrap-up Description';
COMMENT ON COLUMN vwQueueInteractionData.talertcount IS 'Alert Count';
COMMENT ON COLUMN vwQueueInteractionData.talerttimesum IS 'Total Alert Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.talerttimesumDay IS 'Total Alert Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.talerttimemax IS 'Max Alert Time';
COMMENT ON COLUMN vwQueueInteractionData.talerttimemaxDay IS 'Max Alert Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.talerttimemin IS 'Min Alert Time';
COMMENT ON COLUMN vwQueueInteractionData.talerttimeminDay IS 'Min Alert Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tansweredcount IS 'Answered Count';
COMMENT ON COLUMN vwQueueInteractionData.tansweredtimesum IS 'Total Answered Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tansweredtimesumDay IS 'Total Answered Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tansweredtimemax IS 'Max Answered Time';
COMMENT ON COLUMN vwQueueInteractionData.tansweredtimemaxDay IS 'Max Answered Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tansweredtimemin IS 'Min Answered Time';
COMMENT ON COLUMN vwQueueInteractionData.tansweredtimeminDay IS 'Min Answered Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.ttalkcount IS 'Talk Count';
COMMENT ON COLUMN vwQueueInteractionData.ttalktimesum IS 'Total Talk Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.ttalktimesumDay IS 'Total Talk Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.ttalktimemax IS 'Max Talk Time';
COMMENT ON COLUMN vwQueueInteractionData.ttalktimemaxDay IS 'Max Talk Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.ttalktimemin IS 'Min Talk Time';
COMMENT ON COLUMN vwQueueInteractionData.ttalktimeminDay IS 'Min Talk Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.ttalkcompletecount IS 'Talk Complete Count';
COMMENT ON COLUMN vwQueueInteractionData.ttalkcompletetimesum IS 'Total Talk Complete Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.ttalkcompletetimesumDay IS 'Total Talk Complete Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.ttalkcompletetimemax IS 'Max Talk Complete Time';
COMMENT ON COLUMN vwQueueInteractionData.ttalkcompletetimemaxDay IS 'Max Talk Complete Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.ttalkcompletetimemin IS 'Min Talk Complete Time';
COMMENT ON COLUMN vwQueueInteractionData.ttalkcompletetimeminDay IS 'Min Talk Complete Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tnotrespondingcount IS 'Not Responding Count';
COMMENT ON COLUMN vwQueueInteractionData.tnotrespondingtimesum IS 'Total Not Responding Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tnotrespondingtimesumDay IS 'Total Not Responding Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tnotrespondingtimemax IS 'Max Not Responding Time';
COMMENT ON COLUMN vwQueueInteractionData.tnotrespondingtimemaxDay IS 'Max Not Responding Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tnotrespondingtimemin IS 'Min Not Responding Time';
COMMENT ON COLUMN vwQueueInteractionData.tnotrespondingtimeminDay IS 'Min Not Responding Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.theldcount IS 'Held Count';
COMMENT ON COLUMN vwQueueInteractionData.theldtimesum IS 'Total Held Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.theldtimesumDay IS 'Total Held Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.theldtimemax IS 'Max Held Time';
COMMENT ON COLUMN vwQueueInteractionData.theldtimemaxDay IS 'Max Held Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.theldtimemin IS 'Min Held Time';
COMMENT ON COLUMN vwQueueInteractionData.theldtimeminDay IS 'Min Held Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.theldcompletecount IS 'Held Complete Count';
COMMENT ON COLUMN vwQueueInteractionData.theldcompletetimesum IS 'Total Held Complete Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.theldcompletetimesumDay IS 'Total Held Complete Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.theldcompletetimemax IS 'Max Held Complete Time';
COMMENT ON COLUMN vwQueueInteractionData.theldcompletetimemaxDay IS 'Max Held Complete Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.theldcompletetimemin IS 'Min Held Complete Time';
COMMENT ON COLUMN vwQueueInteractionData.theldcompletetimeminDay IS 'Min Held Complete Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.thandlecount IS 'Handle Count';
COMMENT ON COLUMN vwQueueInteractionData.thandletimesum IS 'Total Handle Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.thandletimesumDay IS 'Total Handle Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.thandletimemax IS 'Max Handle Time';
COMMENT ON COLUMN vwQueueInteractionData.thandletimemaxDay IS 'Max Handle Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.thandletimemin IS 'Min Handle Time';
COMMENT ON COLUMN vwQueueInteractionData.thandletimeminDay IS 'Min Handle Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tacwcount IS 'ACW Count';
COMMENT ON COLUMN vwQueueInteractionData.tacwtimesum IS 'Total ACW Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tacwtimesumDay IS 'Total ACW Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tacwtimemax IS 'Max ACW Time';
COMMENT ON COLUMN vwQueueInteractionData.tacwtimemaxDay IS 'Max ACW Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tacwtimemin IS 'Min ACW Time';
COMMENT ON COLUMN vwQueueInteractionData.tacwtimeminDay IS 'Min ACW Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.nconsult IS 'Consult Count';
COMMENT ON COLUMN vwQueueInteractionData.nconsulttransferred IS 'Consult Transferred Count';
COMMENT ON COLUMN vwQueueInteractionData.noutbound IS 'Outbound Count';
COMMENT ON COLUMN vwQueueInteractionData.nerror IS 'Error Count';
COMMENT ON COLUMN vwQueueInteractionData.ntransferred IS 'Transferred Count';
COMMENT ON COLUMN vwQueueInteractionData.nblindtransferred IS 'Blind Transferred Count';
COMMENT ON COLUMN vwQueueInteractionData.nconnected IS 'Connected Count';
COMMENT ON COLUMN vwQueueInteractionData.noffered IS 'Offered Count';
COMMENT ON COLUMN vwQueueInteractionData.noversla IS 'Over SLA Count';
COMMENT ON COLUMN vwQueueInteractionData.tacdcount IS 'ACD Count';
COMMENT ON COLUMN vwQueueInteractionData.tacdtimesum IS 'Total ACD Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tacdtimesumDay IS 'Total ACD Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tacdtimemax IS 'Max ACD Time';
COMMENT ON COLUMN vwQueueInteractionData.tacdtimemaxDay IS 'Max ACD Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tacdtimemin IS 'Min ACD Time';
COMMENT ON COLUMN vwQueueInteractionData.tacdtimeminDay IS 'Min ACD Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tdialingcount IS 'Dialing Count';
COMMENT ON COLUMN vwQueueInteractionData.tdialingtimesum IS 'Total Dialing Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tdialingtimesumDay IS 'Total Dialing Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tdialingtimemax IS 'Max Dialing Time';
COMMENT ON COLUMN vwQueueInteractionData.tdialingtimemaxDay IS 'Max Dialing Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tdialingtimemin IS 'Min Dialing Time';
COMMENT ON COLUMN vwQueueInteractionData.tdialingtimeminDay IS 'Min Dialing Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tcontactingcount IS 'Contacting Count';
COMMENT ON COLUMN vwQueueInteractionData.tcontactingtimesum IS 'Total Contacting Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tcontactingtimesumDay IS 'Total Contacting Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tcontactingtimemax IS 'Max Contacting Time';
COMMENT ON COLUMN vwQueueInteractionData.tcontactingtimemaxDay IS 'Max Contacting Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tcontactingtimemin IS 'Min Contacting Time';
COMMENT ON COLUMN vwQueueInteractionData.tcontactingtimeminDay IS 'Min Contacting Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tvoicemailcount IS 'Voicemail Count';
COMMENT ON COLUMN vwQueueInteractionData.tvoicemailtimesum IS 'Total Voicemail Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tvoicemailtimesumDay IS 'Total Voicemail Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tvoicemailtimemax IS 'Max Voicemail Time';
COMMENT ON COLUMN vwQueueInteractionData.tvoicemailtimemaxDay IS 'Max Voicemail Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tvoicemailtimemin IS 'Min Voicemail Time';
COMMENT ON COLUMN vwQueueInteractionData.tvoicemailtimeminDay IS 'Min Voicemail Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tflowoutcount IS 'Flowout Count';
COMMENT ON COLUMN vwQueueInteractionData.tflowouttimesum IS 'Total Flowout Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tflowouttimesumDay IS 'Total Flowout Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tflowouttimemax IS 'Max Flowout Time';
COMMENT ON COLUMN vwQueueInteractionData.tflowouttimemaxDay IS 'Max Flowout Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tflowouttimemin IS 'Min Flowout Time';
COMMENT ON COLUMN vwQueueInteractionData.tflowouttimeminDay IS 'Min Flowout Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.twaitcount IS 'Wait Count';
COMMENT ON COLUMN vwQueueInteractionData.twaittimesum IS 'Total Wait Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.twaittimesumDay IS 'Total Wait Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.twaittimemax IS 'Max Wait Time';
COMMENT ON COLUMN vwQueueInteractionData.twaittimemaxDay IS 'Max Wait Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.twaittimemin IS 'Min Wait Time';
COMMENT ON COLUMN vwQueueInteractionData.twaittimeminDay IS 'Min Wait Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.twaittimeavg IS 'Average Wait Time';
COMMENT ON COLUMN vwQueueInteractionData.tabandoncount IS 'Abandon Count';
COMMENT ON COLUMN vwQueueInteractionData.tabandontimesum IS 'Total Abandon Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tabandontimesumDay IS 'Total Abandon Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tabandontimemax IS 'Max Abandon Time';
COMMENT ON COLUMN vwQueueInteractionData.tabandontimemaxDay IS 'Max Abandon Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tabandontimemin IS 'Min Abandon Time';
COMMENT ON COLUMN vwQueueInteractionData.tabandontimeminDay IS 'Min Abandon Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tabandontimeavg IS 'Average Abandon Time';
COMMENT ON COLUMN vwQueueInteractionData.servicelevelnumerator IS 'Service Level Numerator';
COMMENT ON COLUMN vwQueueInteractionData.serviceleveldenominator IS 'Service Level Denominator';
COMMENT ON COLUMN vwQueueInteractionData.servicelevelcalc IS 'Service Level Calculation';
COMMENT ON COLUMN vwQueueInteractionData.av1count IS 'AV1 Count';
COMMENT ON COLUMN vwQueueInteractionData.av1timesum IS 'Total AV1 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av1timesumDay IS 'Total AV1 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av1timemax IS 'Max AV1 Time';
COMMENT ON COLUMN vwQueueInteractionData.av1timemaxDay IS 'Max AV1 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av1timemin IS 'Min AV1 Time';
COMMENT ON COLUMN vwQueueInteractionData.av1timeminDay IS 'Min AV1 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av2count IS 'AV2 Count';
COMMENT ON COLUMN vwQueueInteractionData.av2timesum IS 'Total AV2 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av2timesumDay IS 'Total AV2 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av2timemax IS 'Max AV2 Time';
COMMENT ON COLUMN vwQueueInteractionData.av2timemaxDay IS 'Max AV2 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av2timemin IS 'Min AV2 Time';
COMMENT ON COLUMN vwQueueInteractionData.av2timeminDay IS 'Min AV2 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av3count IS 'AV3 Count';
COMMENT ON COLUMN vwQueueInteractionData.av3timesum IS 'Total AV3 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av3timesumDay IS 'Total AV3 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av3timemax IS 'Max AV3 Time';
COMMENT ON COLUMN vwQueueInteractionData.av3timemaxDay IS 'Max AV3 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av3timemin IS 'Min AV3 Time';
COMMENT ON COLUMN vwQueueInteractionData.av3timeminDay IS 'Min AV3 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av4count IS 'AV4 Count';
COMMENT ON COLUMN vwQueueInteractionData.av4timesum IS 'Total AV4 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av4timesumDay IS 'Total AV4 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av4timemax IS 'Max AV4 Time';
COMMENT ON COLUMN vwQueueInteractionData.av4timemaxDay IS 'Max AV4 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av4timemin IS 'Min AV4 Time';
COMMENT ON COLUMN vwQueueInteractionData.av4timeminDay IS 'Min AV4 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av5count IS 'AV5 Count';
COMMENT ON COLUMN vwQueueInteractionData.av5timesum IS 'Total AV5 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av5timesumDay IS 'Total AV5 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av5timemax IS 'Max AV5 Time';
COMMENT ON COLUMN vwQueueInteractionData.av5timemaxDay IS 'Max AV5 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av5timemin IS 'Min AV5 Time';
COMMENT ON COLUMN vwQueueInteractionData.av5timeminDay IS 'Min AV5 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av6count IS 'AV6 Count';
COMMENT ON COLUMN vwQueueInteractionData.av6timesum IS 'Total AV6 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av6timesumDay IS 'Total AV6 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av6timemax IS 'Max AV6 Time';
COMMENT ON COLUMN vwQueueInteractionData.av6timemaxDay IS 'Max AV6 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av6timemin IS 'Min AV6 Time';
COMMENT ON COLUMN vwQueueInteractionData.av6timeminDay IS 'Min AV6 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av7count IS 'AV7 Count';
COMMENT ON COLUMN vwQueueInteractionData.av7timesum IS 'Total AV7 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av7timesumDay IS 'Total AV7 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av7timemax IS 'Max AV7 Time';
COMMENT ON COLUMN vwQueueInteractionData.av7timemaxDay IS 'Max AV7 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av7timemin IS 'Min AV7 Time';
COMMENT ON COLUMN vwQueueInteractionData.av7timeminDay IS 'Min AV7 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av8count IS 'AV8 Count';
COMMENT ON COLUMN vwQueueInteractionData.av8timesum IS 'Total AV8 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av8timesumDay IS 'Total AV8 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av8timemax IS 'Max AV8 Time';
COMMENT ON COLUMN vwQueueInteractionData.av8timemaxDay IS 'Max AV8 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av8timemin IS 'Min AV8 Time';
COMMENT ON COLUMN vwQueueInteractionData.av8timeminDay IS 'Min AV8 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av9count IS 'AV9 Count';
COMMENT ON COLUMN vwQueueInteractionData.av9timesum IS 'Total AV9 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av9timesumDay IS 'Total AV9 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av9timemax IS 'Max AV9 Time';
COMMENT ON COLUMN vwQueueInteractionData.av9timemaxDay IS 'Max AV9 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av9timemin IS 'Min AV9 Time';
COMMENT ON COLUMN vwQueueInteractionData.av9timeminDay IS 'Min AV9 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av10count IS 'AV10 Count';
COMMENT ON COLUMN vwQueueInteractionData.av10timesum IS 'Total AV10 Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.av10timesumDay IS 'Total AV10 Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av10timemax IS 'Max AV10 Time';
COMMENT ON COLUMN vwQueueInteractionData.av10timemaxDay IS 'Max AV10 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.av10timemin IS 'Min AV10 Time';
COMMENT ON COLUMN vwQueueInteractionData.av10timeminDay IS 'Min AV10 Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.oservicetarget IS 'Service Target Count';
COMMENT ON COLUMN vwQueueInteractionData.tivrcount IS 'Total IVR Count';
COMMENT ON COLUMN vwQueueInteractionData.tivrtimesum IS 'Total IVR Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tivrtimesumDay IS 'Total IVR Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tivrtimemax IS 'Max IVR Time';
COMMENT ON COLUMN vwQueueInteractionData.tivrtimemaxDay IS 'Max IVR Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tivrtimemin IS 'Min IVR Time';
COMMENT ON COLUMN vwQueueInteractionData.tivrtimeminDay IS 'Min IVR Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tshortabandoncount IS 'Total Short Abandon Count';
COMMENT ON COLUMN vwQueueInteractionData.tshortabandontimesum IS 'Total Short Abandon Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tshortabandontimesumDay IS 'Total Short Abandon Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tshortabandontimemax IS 'Max Short Abandon Time';
COMMENT ON COLUMN vwQueueInteractionData.tshortabandontimemaxDay IS 'Max Short Abandon Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tshortabandontimemin IS 'Min Short Abandon Time';
COMMENT ON COLUMN vwQueueInteractionData.tshortabandontimeminDay IS 'Min Short Abandon Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tuserresponsetimecount IS 'Total User Response Time Count';
COMMENT ON COLUMN vwQueueInteractionData.tuserresponsetimetimesum IS 'Total User Response Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tuserresponsetimetimesumDay IS 'Total User Response Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tuserresponsetimetimemax IS 'Max User Response Time';
COMMENT ON COLUMN vwQueueInteractionData.tuserresponsetimetimemaxDay IS 'Max User Response Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tuserresponsetimetimemin IS 'Min User Response Time';
COMMENT ON COLUMN vwQueueInteractionData.tuserresponsetimetimeminDay IS 'Min User Response Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tagentresponsetimecount IS 'Total Agent Response Time Count';
COMMENT ON COLUMN vwQueueInteractionData.tagentresponsetimetimesum IS 'Total Agent Response Time Sum';
COMMENT ON COLUMN vwQueueInteractionData.tagentresponsetimetimesumDay IS 'Total Agent Response Time Sum (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tagentresponsetimetimemax IS 'Max Agent Response Time';
COMMENT ON COLUMN vwQueueInteractionData.tagentresponsetimetimemaxDay IS 'Max Agent Response Time (Days)';
COMMENT ON COLUMN vwQueueInteractionData.tagentresponsetimetimemin IS 'Min Agent Response Time';
COMMENT ON COLUMN vwQueueInteractionData.tagentresponsetimetimeminDay IS 'Min Agent Response Time (Days)';

COMMENT ON VIEW vwQueueInteractionData IS 'Queue Interaction Data Interval Data - Interval is from (15-60) Min(s)';
