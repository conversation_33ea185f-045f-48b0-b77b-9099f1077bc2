CREATE TABLE IF NOT EXISTS learningmodules (
    id VARCHAR(50) NOT NULL,
    name <PERSON><PERSON><PERSON><PERSON>(100),
    description VARCHAR(500),
    version VARCHAR(255),
    externalId VARCHAR(50),
    source VARCHAR(50),
    enforceContentOrder BOOLEAN,
    isArchived BOOLEAN,
    isPublished BOOLEAN,
    completionTimeInDays INTEGER,
    type VARCHAR(50),
    dateCreated TIMESTAMP WITHOUT TIME ZONE,
    dateModified TIMESTAMP WITHOUT TIME ZONE,
    lengthInMinutes numeric(20, 2),
    updated timestamp without time zone,
    CONSTRAINT learningmodule_pkey PRIMARY KEY (id)
);

ALTER TABLE learningmodules
DROP COLUMN IF EXISTS state;

-- Update description column length for existing installations
-- Note: Snowflake doesn't support conditional DDL like PostgreSQL's DO blocks
-- This ALTER will only succeed if the table exists and the column needs modification
-- If the column is already VARCHAR(500) or larger, this will be a no-op
ALTER TABLE learningmodules MODIFY COLUMN description VARCHAR(500);
