DROP VIEW IF EXISTS vwUserInteractionData;
CREATE
OR REPLACE VIEW vwuserinteractiondata AS
SELECT
    userinteractiondata.keyid,
    userinteractiondata.startdate as startdateUTC,
    userinteractiondata.startDateLTC,
    userinteractiondata.userid,
    userdetail.name as agentname,
    userdetail.managerid as managerid,
    userdetail.managername,
    userdetail.divisionid as agentdivisionid,
    userdetail.divisionname as agentdivisionname,
    userinteractiondata.direction,
    userinteractiondata.queueid,
    queuedetail.name as queuename,
    queuedetail.divisionid,
    queuedetail.divisionname,
    userinteractiondata.mediatype,
    userinteractiondata.wrapupcode,
    wrapupdetails.name as wrapupdesc,
    userinteractiondata.talertcount,
    userinteractiondata.talerttimesum,
    userinteractiondata.talerttimesum / 86400.00 as talerttimesumDay,
    userinteractiondata.talerttimemax,
    userinteractiondata.talerttimemax / 86400.00 as talerttimemaxDay,
    userinteractiondata.talerttimemin,
    userinteractiondata.talerttimemin / 86400.00 as talerttimeminDay,
    userinteractiondata.tansweredcount,
    userinteractiondata.tansweredtimesum,
    userinteractiondata.tansweredtimesum / 86400.00 as tansweredtimesumDay,
    userinteractiondata.tansweredtimemax,
    userinteractiondata.tansweredtimemax / 86400.00 as tansweredtimemaxDay,
    userinteractiondata.tansweredtimemin,
    userinteractiondata.tansweredtimemin / 86400.00 as tansweredtimeminDay,
    userinteractiondata.ttalkcount,
    userinteractiondata.ttalktimesum,
    userinteractiondata.ttalktimesum / 86400.00 as ttalktimesumDay,
    userinteractiondata.ttalktimemax,
    userinteractiondata.ttalktimemax / 86400.00 as ttalktimemaxDay,
    userinteractiondata.ttalktimemin,
    userinteractiondata.ttalktimemin / 86400.00 as ttalktimeminDay,
    userinteractiondata.ttalkcompletecount,
    userinteractiondata.ttalkcompletetimesum,
    userinteractiondata.ttalkcompletetimesum / 86400.00 as ttalkcompletetimesumDay,
    userinteractiondata.ttalkcompletetimemax,
    userinteractiondata.ttalkcompletetimemax / 86400.00 as ttalkcompletetimemaxDay,
    userinteractiondata.ttalkcompletetimemin,
    userinteractiondata.ttalkcompletetimemin / 86400.00 as ttalkcompletetimeminDay,
    userinteractiondata.tnotrespondingcount,
    userinteractiondata.tnotrespondingtimesum,
    userinteractiondata.tnotrespondingtimesum / 86400.00 as tnotrespondingtimesumDay,
    userinteractiondata.tnotrespondingtimemax,
    userinteractiondata.tnotrespondingtimemax / 86400.00 as tnotrespondingtimemaxDay,
    userinteractiondata.tnotrespondingtimemin,
    userinteractiondata.tnotrespondingtimemin / 86400.00 as tnotrespondingtimeminDay,
    userinteractiondata.theldcount,
    userinteractiondata.theldtimesum,
    userinteractiondata.theldtimesum / 86400.00 as theldtimesumDay,
    userinteractiondata.theldtimemax,
    userinteractiondata.theldtimemax / 86400.00 as theldtimemaxDay,
    userinteractiondata.theldtimemin,
    userinteractiondata.theldtimemin / 86400.00 as theldtimeminDay,
    userinteractiondata.theldcompletecount,
    userinteractiondata.theldcompletetimesum,
    userinteractiondata.theldcompletetimesum / 86400.00 as theldcompletetimesumDay,
    userinteractiondata.theldcompletetimemax,
    userinteractiondata.theldcompletetimemax / 86400.00 as theldcompletetimemaxDay,
    userinteractiondata.theldcompletetimemin,
    userinteractiondata.theldcompletetimemin / 86400.00 as theldcompletetimeminDay,
    userinteractiondata.thandlecount,
    userinteractiondata.thandletimesum,
    userinteractiondata.thandletimesum / 86400.00 as thandletimesumDay,
    userinteractiondata.thandletimemax,
    userinteractiondata.thandletimemax / 86400.00 as thandletimemaxDay,
    userinteractiondata.thandletimemin,
    userinteractiondata.thandletimemin / 86400.00 as thandletimeminDay,
    userinteractiondata.tacwcount,
    userinteractiondata.tacwtimesum,
    userinteractiondata.tacwtimesum / 86400.00 as tacwtimesumDay,
    userinteractiondata.tacwtimemax,
    userinteractiondata.tacwtimemax / 86400.00 as tacwtimemaxDay,
    userinteractiondata.tacwtimemin,
    userinteractiondata.tacwtimemin / 86400.00 as tacwtimeminDay,
    userinteractiondata.nconsult,
    userinteractiondata.nconsulttransferred,
    userinteractiondata.noutbound,
    userinteractiondata.nerror,
    userinteractiondata.ntransferred,
    userinteractiondata.nblindtransferred,
    userinteractiondata.nconnected,
    userinteractiondata.tdialingcount,
    userinteractiondata.tdialingtimesum,
    userinteractiondata.tdialingtimesum / 86400.00 as tdialingtimesumDay,
    userinteractiondata.tdialingtimemax,
    userinteractiondata.tdialingtimemax / 86400.00 as tdialingtimemaxDay,
    userinteractiondata.tdialingtimemin,
    userinteractiondata.tdialingtimemin / 86400.00 as tdialingtimeminDay,
    userinteractiondata.tcontactingcount,
    userinteractiondata.tcontactingtimesum,
    userinteractiondata.tcontactingtimesum / 86400.00 as tcontactingtimesumDay,
    userinteractiondata.tcontactingtimemax,
    userinteractiondata.tcontactingtimemax / 86400.00 as tcontactingtimemaxDay,
    userinteractiondata.tcontactingtimemin,
    userinteractiondata.tcontactingtimemin / 86400.00 as tcontactingtimeminDay,
    userinteractiondata.tvoicemailcount,
    userinteractiondata.tvoicemailtimesum,
    userinteractiondata.tvoicemailtimesum / 86400.00 as tvoicemailtimesumDay,
    userinteractiondata.tvoicemailtimemax,
    userinteractiondata.tvoicemailtimemax / 86400.00 as tvoicemailtimemaxDay,
    userinteractiondata.tvoicemailtimemin,
    userinteractiondata.tvoicemailtimemin / 86400.00 as tvoicemailtimeminDay,
    userinteractiondata.noutboundabandoned,
    userinteractiondata.noutboundattempted,
    userinteractiondata.noutboundconnected,
    userinteractiondata.nstatetransitionerror,
    userinteractiondata.oexternalmediacount,
    userinteractiondata.tmonitoringcount,
    userinteractiondata.tmonitoringtimesum,
    userinteractiondata.tmonitoringtimesum / 86400.00 as tmonitoringtimesumDay,
    userinteractiondata.tmonitoringtimemax,
    userinteractiondata.tmonitoringtimemax / 86400.00 as tmonitoringtimemaxDay,
    userinteractiondata.tmonitoringtimemin,
    userinteractiondata.tmonitoringtimemin / 86400.00 as tmonitoringtimeminDay,
    userinteractiondata.updated
FROM
    userInteractionData userInteractionData
    left outer join vwUserDetail userdetail on userdetail.id = userinteractiondata.userid
    left outer join vwqueuedetails queuedetail on queuedetail.id = userinteractiondata.queueid
    left outer join wrapupDetails wrapupdetails on wrapupdetails.id = userinteractiondata.wrapupcode;

-- Add comments

COMMENT ON COLUMN vwUserInteractionData.agentname IS 'Agent Name';
COMMENT ON COLUMN vwUserInteractionData.direction IS 'Interaction Direction';
COMMENT ON COLUMN vwUserInteractionData.keyid IS 'Primary Key';
COMMENT ON COLUMN vwUserInteractionData.managerid IS 'Manager GUID';
COMMENT ON COLUMN vwUserInteractionData.managername IS 'Manager Name';
COMMENT ON COLUMN vwUserInteractionData.mediatype IS 'Media Type';
COMMENT ON COLUMN vwUserInteractionData.nblindtransferred IS 'Number of Blind Transfers';
COMMENT ON COLUMN vwUserInteractionData.nconnected IS 'Number of Connected Calls';
COMMENT ON COLUMN vwUserInteractionData.nconsult IS 'Number of Consult Calls';
COMMENT ON COLUMN vwUserInteractionData.nconsulttransferred IS 'Number of Consult Transfers';
COMMENT ON COLUMN vwUserInteractionData.nerror IS 'Number of Errors';
COMMENT ON COLUMN vwUserInteractionData.noutbound IS 'Number of Outbound Calls';
COMMENT ON COLUMN vwUserInteractionData.ntransferred IS 'Number of Transfers';
COMMENT ON COLUMN vwUserInteractionData.queueid IS 'Queue GUID';
COMMENT ON COLUMN vwUserInteractionData.queuename IS 'Queue Name';
COMMENT ON COLUMN vwUserInteractionData.startDateLTC IS 'Start Date (LTC)';
COMMENT ON COLUMN vwUserInteractionData.startdateUTC IS 'Start Date (UTC)';
COMMENT ON COLUMN vwUserInteractionData.tacwcount IS 'ACW Count';
COMMENT ON COLUMN vwUserInteractionData.tacwtimemax IS 'Maximum ACW Time';
COMMENT ON COLUMN vwUserInteractionData.tacwtimemaxDay IS 'Maximum ACW Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tacwtimemin IS 'Minimum ACW Time';
COMMENT ON COLUMN vwUserInteractionData.tacwtimeminDay IS 'Minimum ACW Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tacwtimesum IS 'Total ACW Time';
COMMENT ON COLUMN vwUserInteractionData.tacwtimesumDay IS 'Total ACW Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.talertcount IS 'Alert Count';
COMMENT ON COLUMN vwUserInteractionData.talerttimemax IS 'Maximum Alert Time';
COMMENT ON COLUMN vwUserInteractionData.talerttimemaxDay IS 'Maximum Alert Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.talerttimemin IS 'Minimum Alert Time';
COMMENT ON COLUMN vwUserInteractionData.talerttimeminDay IS 'Minimum Alert Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.talerttimesum IS 'Total Alert Time';
COMMENT ON COLUMN vwUserInteractionData.talerttimesumDay IS 'Total Alert Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tansweredcount IS 'Answered Count';
COMMENT ON COLUMN vwUserInteractionData.tansweredtimemax IS 'Maximum Answered Time';
COMMENT ON COLUMN vwUserInteractionData.tansweredtimemaxDay IS 'Maximum Answered Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tansweredtimemin IS 'Minimum Answered Time';
COMMENT ON COLUMN vwUserInteractionData.tansweredtimeminDay IS 'Minimum Answered Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tansweredtimesum IS 'Total Answered Time';
COMMENT ON COLUMN vwUserInteractionData.tansweredtimesumDay IS 'Total Answered Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tcontactingcount IS 'Contacting Count';
COMMENT ON COLUMN vwUserInteractionData.tcontactingtimemax IS 'Maximum Contacting Time';
COMMENT ON COLUMN vwUserInteractionData.tcontactingtimemaxDay IS 'Maximum Contacting Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tcontactingtimemin IS 'Minimum Contacting Time';
COMMENT ON COLUMN vwUserInteractionData.tcontactingtimeminDay IS 'Minimum Contacting Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tcontactingtimesum IS 'Total Contacting Time';
COMMENT ON COLUMN vwUserInteractionData.tcontactingtimesumDay IS 'Total Contacting Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tdialingcount IS 'Dialing Count';
COMMENT ON COLUMN vwUserInteractionData.tdialingtimemax IS 'Maximum Dialing Time';
COMMENT ON COLUMN vwUserInteractionData.tdialingtimemaxDay IS 'Maximum Dialing Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tdialingtimemin IS 'Minimum Dialing Time';
COMMENT ON COLUMN vwUserInteractionData.tdialingtimeminDay IS 'Minimum Dialing Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tdialingtimesum IS 'Total Dialing Time';
COMMENT ON COLUMN vwUserInteractionData.tdialingtimesumDay IS 'Total Dialing Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.thandlecount IS 'Handle Count';
COMMENT ON COLUMN vwUserInteractionData.thandletimemax IS 'Maximum Handle Time';
COMMENT ON COLUMN vwUserInteractionData.thandletimemaxDay IS 'Maximum Handle Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.thandletimemin IS 'Minimum Handle Time';
COMMENT ON COLUMN vwUserInteractionData.thandletimeminDay IS 'Minimum Handle Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.thandletimesum IS 'Total Handle Time';
COMMENT ON COLUMN vwUserInteractionData.thandletimesumDay IS 'Total Handle Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.theldcompletecount IS 'Completed Held Count';
COMMENT ON COLUMN vwUserInteractionData.theldcompletetimemax IS 'Maximum Completed Held Time';
COMMENT ON COLUMN vwUserInteractionData.theldcompletetimemaxDay IS 'Maximum Completed Held Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.theldcompletetimemin IS 'Minimum Completed Held Time';
COMMENT ON COLUMN vwUserInteractionData.theldcompletetimeminDay IS 'Minimum Completed Held Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.theldcompletetimesum IS 'Total Completed Held Time';
COMMENT ON COLUMN vwUserInteractionData.theldcompletetimesumDay IS 'Total Completed Held Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.theldcount IS 'Held Count';
COMMENT ON COLUMN vwUserInteractionData.theldtimemax IS 'Maximum Held Time';
COMMENT ON COLUMN vwUserInteractionData.theldtimemaxDay IS 'Maximum Held Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.theldtimemin IS 'Minimum Held Time';
COMMENT ON COLUMN vwUserInteractionData.theldtimeminDay IS 'Minimum Held Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.theldtimesum IS 'Total Held Time';
COMMENT ON COLUMN vwUserInteractionData.theldtimesumDay IS 'Total Held Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tnotrespondingcount IS 'Not Responding Count';
COMMENT ON COLUMN vwUserInteractionData.tnotrespondingtimemax IS 'Maximum Not Responding Time';
COMMENT ON COLUMN vwUserInteractionData.tnotrespondingtimemaxDay IS 'Maximum Not Responding Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tnotrespondingtimemin IS 'Minimum Not Responding Time';
COMMENT ON COLUMN vwUserInteractionData.tnotrespondingtimeminDay IS 'Minimum Not Responding Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tnotrespondingtimesum IS 'Total Not Responding Time';
COMMENT ON COLUMN vwUserInteractionData.tnotrespondingtimesumDay IS 'Total Not Responding Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.ttalkcompletecount IS 'Completed Talk Count';
COMMENT ON COLUMN vwUserInteractionData.ttalkcompletetimemax IS 'Maximum Completed Talk Time';
COMMENT ON COLUMN vwUserInteractionData.ttalkcompletetimemaxDay IS 'Maximum Completed Talk Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.ttalkcompletetimemin IS 'Minimum Completed Talk Time';
COMMENT ON COLUMN vwUserInteractionData.ttalkcompletetimeminDay IS 'Minimum Completed Talk Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.ttalkcompletetimesum IS 'Total Completed Talk Time';
COMMENT ON COLUMN vwUserInteractionData.ttalkcompletetimesumDay IS 'Total Completed Talk Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.ttalkcount IS 'Talk Count';
COMMENT ON COLUMN vwUserInteractionData.ttalktimemax IS 'Maximum Talk Time';
COMMENT ON COLUMN vwUserInteractionData.ttalktimemaxDay IS 'Maximum Talk Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.ttalktimemin IS 'Minimum Talk Time';
COMMENT ON COLUMN vwUserInteractionData.ttalktimeminDay IS 'Minimum Talk Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.ttalktimesum IS 'Total Talk Time';
COMMENT ON COLUMN vwUserInteractionData.ttalktimesumDay IS 'Total Talk Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tvoicemailcount IS 'Voicemail Count';
COMMENT ON COLUMN vwUserInteractionData.tvoicemailtimemax IS 'Maximum Voicemail Time';
COMMENT ON COLUMN vwUserInteractionData.tvoicemailtimemaxDay IS 'Maximum Voicemail Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tvoicemailtimemin IS 'Minimum Voicemail Time';
COMMENT ON COLUMN vwUserInteractionData.tvoicemailtimeminDay IS 'Minimum Voicemail Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tvoicemailtimesum IS 'Total Voicemail Time';
COMMENT ON COLUMN vwUserInteractionData.tvoicemailtimesumDay IS 'Total Voicemail Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.noutboundabandoned IS 'Outbound Abandoned Count';
COMMENT ON COLUMN vwUserInteractionData.noutboundattempted IS 'Outbound Attempted Count';
COMMENT ON COLUMN vwUserInteractionData.noutboundconnected IS 'Outbound Connected Count';
COMMENT ON COLUMN vwUserInteractionData.nstatetransitionerror IS 'State Transition Error Count';
COMMENT ON COLUMN vwUserInteractionData.oexternalmediacount IS 'External Media Count';
COMMENT ON COLUMN vwUserInteractionData.tmonitoringcount IS 'Total Monitoring Count';
COMMENT ON COLUMN vwUserInteractionData.tmonitoringtimesum IS 'Total Monitoring Time Sum';
COMMENT ON COLUMN vwUserInteractionData.tmonitoringtimesumDay IS 'Total Monitoring Time Sum (Days)';
COMMENT ON COLUMN vwUserInteractionData.tmonitoringtimemax IS 'Max Monitoring Time';
COMMENT ON COLUMN vwUserInteractionData.tmonitoringtimemaxDay IS 'Max Monitoring Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.tmonitoringtimemin IS 'Min Monitoring Time';
COMMENT ON COLUMN vwUserInteractionData.tmonitoringtimeminDay IS 'Min Monitoring Time (Days)';
COMMENT ON COLUMN vwUserInteractionData.userid IS 'User ID';
COMMENT ON COLUMN vwUserInteractionData.wrapupcode IS 'Wrap-up Code';
COMMENT ON COLUMN vwUserInteractionData.wrapupdesc IS 'Wrap-up Description';

COMMENT ON VIEW vwUserInteractionData IS 'See UserInteractionData - Expands all the GUIDs with their lookups';